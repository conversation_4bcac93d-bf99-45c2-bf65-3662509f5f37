# Cloudflare Workers configuration for SourceFlex
name = "sourceflex"
main = ".svelte-kit/cloudflare/_worker.js"
compatibility_date = "2025-04-01"
compatibility_flags = ["nodejs_compat"]

# Static assets configuration for Workers
[assets]
directory = ".svelte-kit/cloudflare"
binding = "ASSETS"

# KV namespace for rate limiting
[[kv_namespaces]]
binding = "RATE_LIMITS"
id = "8033cb0877db44de8517007d8870a0aa"
preview_id = "8033cb0877db44de8517007d8870a0aa"

# Environment variables for production
[vars]
NODE_ENV = "production"
PUBLIC_TURNSTILE_SITE_KEY = "0x4AAAAAABllltaE5hyR1BQ5"
PUBLIC_NHOST_SUBDOMAIN = "pttthnqikxdsxmeccqho"
PUBLIC_NHOST_REGION = "us-west-2"
PUBLIC_GRAPHQL_ENDPOINT = "https://pttthnqikxdsxmeccqho.graphql.us-west-2.nhost.run/v1"
PUBLIC_APP_NAME = "SourceFlex"

# Production secrets (configure these in Cloudflare Dashboard > Workers > Settings > Variables)
# Do not put production secrets in this file!
# PUBLIC_TURNSTILE_SITE_KEY = "configure-in-dashboard"
# TURNSTILE_SECRET_KEY = "configure-in-dashboard"
# NHOST_ADMIN_SECRET = "configure-in-dashboard"

# Development environment for local testing
[env.development]
vars = { NODE_ENV = "development", PUBLIC_TURNSTILE_SITE_KEY = "1x00000000000000000000AA", TURNSTILE_SECRET_KEY = "1x0000000000000000000000000000000AA" }

# Staging environment (optional)
[env.staging]
vars = { NODE_ENV = "staging" }
# Configure staging secrets in Cloudflare Dashboard

# Observability configuration for monitoring and debugging
[observability.logs]
enabled = true

# Custom domain configuration
[[routes]]
pattern = "app.sourceflex.io/*"
zone_name = "sourceflex.io"

[[routes]]
pattern = "sourceflex.io/*"
zone_name = "sourceflex.io"
