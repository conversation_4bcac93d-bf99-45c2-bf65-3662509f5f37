// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			// Enhanced nHost integration - minimal server-side auth state
			hasToken?: boolean; // Just indicates if auth token present
		}
		// interface PageData {}
		// interface PageState {}
		interface Platform {
			env?: {
				RATE_LIMITS?: KVNamespace;
				NHOST_ADMIN_SECRET?: string;
				TURNSTILE_SECRET_KEY?: string;
			};
		}
	}
}

export {};
