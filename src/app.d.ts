// See https://kit.svelte.dev/docs/types#app
// for information about these interfaces

declare global {
	namespace NodeJS {
		interface ProcessEnv {
			NHOST_ADMIN_SECRET?: string;
			TURNSTILE_SECRET_KEY?: string;
			NODE_ENV?: string;
		}
	}

	namespace App {
		// interface Error {}
		interface Locals {
			// Enhanced nHost integration - minimal server-side auth state
			hasToken?: boolean; // Just indicates if auth token present
			user?: {
				id: string;
				email: string;
				displayName?: string;
			} | null;
		}
		// interface PageData {}
		// interface PageState {}
		interface Platform {
			env?: {
				RATE_LIMITS?: KVNamespace;
				NHOST_ADMIN_SECRET?: string;
				TURNSTILE_SECRET_KEY?: string;
			};
		}
	}
}

export {};
