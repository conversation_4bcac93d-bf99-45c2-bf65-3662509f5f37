import { z } from 'zod';

// Client (Organization) schema
export const createClientSchema = z.object({
  name: z.string().min(1, 'Client name is required'),
  industry: z.string().optional(),
  website: z.string().url('Invalid website URL').optional().or(z.literal('')),
});

export type CreateClientValues = z.infer<typeof createClientSchema>;

// Job schema
export const createJobSchema = z.object({
  title: z.string().min(1, 'Job title is required'),
  description: z.string().optional(),
  status: z.enum(['draft', 'active', 'paused', 'closed']).default('draft'),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).default('medium'),
  job_type: z.enum(['full_time', 'part_time', 'contract', 'temporary', 'internship']),
  work_arrangement: z.enum(['remote', 'hybrid', 'onsite']),
  experience_level: z.enum(['entry', 'mid', 'senior', 'lead', 'executive']),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  min_compensation: z.number().min(0).optional(),
  max_compensation: z.number().min(0).optional(),
  compensation_frequency: z.enum(['hourly', 'daily', 'weekly', 'monthly', 'yearly']).default('yearly'),
  currency: z.string().default('USD'),
  client_id: z.string().uuid('Invalid client ID'),
});

export type CreateJobValues = z.infer<typeof createJobSchema>;
