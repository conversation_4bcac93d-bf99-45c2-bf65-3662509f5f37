<script lang="ts">
  import { createForm } from 'sveltekit-superforms';
  import { superForm } from 'sveltekit-superforms';
  import { createJobSchema, type CreateJobValues } from '../schemas/createJobSchema';
  import { CreateJobStore, GetJobsStore } from '$houdini';
  import { toast } from 'svelte-sonner';
  import ClientCreateModal from './ClientCreateModal.svelte';
  
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Form from '$lib/components/ui/form';
  import * as Tabs from '$lib/components/ui/tabs';
  import * as Select from '$lib/components/ui/select';
  import { Input } from '$lib/components/ui/input';
  import { Button } from '$lib/components/ui/button';
  import { Textarea } from '$lib/components/ui/textarea';
  import { Loader2, Plus } from 'lucide-svelte';
  import { onMount } from 'svelte';
  
  // Props
  interface JobCreateModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
  }
  
  const { open = false, onOpenChange } = $props<JobCreateModalProps>();
  
  // Client selection state
  let clients = $state<{ id: string; name: string }[]>([]);
  let isLoadingClients = $state(true);
  let clientCreateModalOpen = $state(false);
  
  // Create the form
  const form = createForm(createJobSchema, {
    resetForm: true,
    validators: {
      title: (value: string | undefined) => {
        if (!value) return 'Job title is required';
        return null;
      },
      client_id: (value: string | undefined) => {
        if (!value) return 'Please select a client';
        return null;
      }
    }
  });
  
  // Initialize the form with SuperForms
  const { form: jobForm, errors, enhance, submitting, reset } = superForm(form, {
    resetForm: true,
    onSubmit: ({ formData, cancel }: { formData: FormData; cancel: () => void }) => {
      // If the form is already submitting, prevent duplicate submissions
      if ($submitting) {
        cancel();
      }
    },
    onResult: ({ result }: { result: { type: string } }) => {
      // Handle form submission result
      if (result.type === 'success') {
        // Form validation succeeded, now submit to GraphQL
        submitJob();
      }
    }
  });
  
  // Create job mutation store
  const createJobStore = new CreateJobStore();
  
  // Get jobs store for refreshing the list after creation
  const getJobsStore = new GetJobsStore();
  
  // Fetch clients on mount
  onMount(async () => {
    try {
      // In a real implementation, you would fetch clients from the API
      // For now, we'll simulate a delay and return mock data
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock client data - in a real app, this would come from a GraphQL query
      clients = [
        { id: "00000000-0000-0000-0000-000000000001", name: "Acme Corporation" },
        { id: "00000000-0000-0000-0000-000000000002", name: "Globex Industries" },
        { id: "00000000-0000-0000-0000-000000000003", name: "Initech" },
        { id: "00000000-0000-0000-0000-000000000004", name: "Umbrella Corp" },
      ];
      
      isLoadingClients = false;
    } catch (error) {
      console.error('Error fetching clients:', error);
      isLoadingClients = false;
      toast.error('Failed to load clients. Please try again.');
    }
  });
  
  // Submit job to GraphQL
  async function submitJob() {
    try {
      // Get form values
      const jobData: CreateJobValues = $jobForm;
      
      // Execute the mutation
      const result = await createJobStore.mutate({
        job: {
          title: jobData.title,
          client_id: jobData.client_id,
          job_type: jobData.job_type,
          priority: jobData.priority,
          status: jobData.status,
          work_arrangement: jobData.work_arrangement,
          primary_address: jobData.primary_address || null,
          city: jobData.city || null,
          state: jobData.state || null,
          country: jobData.country || null,
          zipcode: jobData.zipcode || null,
          min_compensation: jobData.min_compensation || null,
          max_compensation: jobData.max_compensation || null,
          compensation_frequency: jobData.compensation_frequency || null,
          currency: jobData.currency,
          description: jobData.description || null,
          qualifications: jobData.qualifications || null,
          benefits: jobData.benefits || null,
          number_of_openings: jobData.number_of_openings,
          experience_level: jobData.experience_level || null,
          start_date: jobData.start_date || null,
          end_date: jobData.end_date || null,
          deadline: jobData.deadline || null,
          department: jobData.department || null,
          internal_notes: jobData.internal_notes || null,
          created_at: new Date().toISOString()
        }
      });
      
      // Check for errors
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }
      
      // Get the created job
      const createdJob = result.data?.insert_jobs_one;
      
      if (createdJob) {
        // Show success message
        toast.success(`Job "${createdJob.title}" created successfully`);
        
        // Refresh the jobs list
        getJobsStore.fetch({ policy: 'NetworkOnly' });
        
        // Close the modal
        onOpenChange(false);
        
        // Reset the form
        reset();
      }
    } catch (error) {
      console.error('Error creating job:', error);
      toast.error('Failed to create job. Please try again.');
    }
  }
  
  // Handle client creation
  function handleClientCreated(client: { id: string; name: string }) {
    // Add the new client to the list
    clients = [...clients, client];
    
    // Select the newly created client
    jobForm.update((form: any) => ({
      ...form,
      client_id: client.id
    }));
  }
  
  // Handle dialog close
  function handleOpenChange(isOpen: boolean) {
    if (!isOpen && !$submitting) {
      reset();
    }
    onOpenChange(isOpen);
  }
</script>

<Dialog.Root {open} onOpenChange={handleOpenChange}>
  <Dialog.Content class="sm:max-w-[800px]">
    <Dialog.Header>
      <Dialog.Title>Create New Job</Dialog.Title>
      <Dialog.Description>
        Fill out the form below to create a new job listing.
      </Dialog.Description>
    </Dialog.Header>
    
    <form method="POST" use:enhance>
      <Tabs.Root value="basic" class="w-full">
        <Tabs.List class="grid grid-cols-3 mb-4">
          <Tabs.Trigger value="basic">Basic Info</Tabs.Trigger>
          <Tabs.Trigger value="details">Job Details</Tabs.Trigger>
          <Tabs.Trigger value="compensation">Compensation</Tabs.Trigger>
        </Tabs.List>
        
        <!-- Basic Info Tab -->
        <Tabs.Content value="basic" class="space-y-4">
          <Form.Field {errors} name="title">
            <Form.Label>Job Title <span class="text-destructive">*</span></Form.Label>
            <Form.Input>
              <Input 
                name="title" 
                bind:value={$jobForm.title} 
                placeholder="e.g. Senior Software Engineer" 
                required 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
          
          <div class="flex items-end gap-2">
            <div class="flex-1">
              <Form.Field {errors} name="client_id">
                <Form.Label>Client <span class="text-destructive">*</span></Form.Label>
                <Select.Root 
                  name="client_id" 
                  bind:value={$jobForm.client_id}
                >
                  <Form.Input>
                    <Select.Trigger class="w-full" disabled={isLoadingClients}>
                      <Select.Value placeholder="Select a client" />
                    </Select.Trigger>
                  </Form.Input>
                  <Select.Content>
                    {#if isLoadingClients}
                      <Select.Item value="loading" disabled>
                        <div class="flex items-center">
                          <Loader2 class="h-4 w-4 mr-2 animate-spin" />
                          Loading clients...
                        </div>
                      </Select.Item>
                    {:else if clients.length === 0}
                      <Select.Item value="none" disabled>No clients found</Select.Item>
                    {:else}
                      {#each clients as client}
                        <Select.Item value={client.id}>{client.name}</Select.Item>
                      {/each}
                    {/if}
                  </Select.Content>
                </Select.Root>
                <Form.FieldErrors />
              </Form.Field>
            </div>
            
            <Button 
              type="button" 
              variant="outline" 
              size="icon" 
              class="mb-1"
              on:click={() => (clientCreateModalOpen = true)}
            >
              <Plus class="h-4 w-4" />
            </Button>
          </div>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field {errors} name="job_type">
              <Form.Label>Job Type <span class="text-destructive">*</span></Form.Label>
              <Select.Root 
                name="job_type" 
                bind:value={$jobForm.job_type}
              >
                <Form.Input>
                  <Select.Trigger class="w-full">
                    <Select.Value placeholder="Select job type" />
                  </Select.Trigger>
                </Form.Input>
                <Select.Content>
                  <Select.Item value="full_time">Full Time</Select.Item>
                  <Select.Item value="part_time">Part Time</Select.Item>
                  <Select.Item value="contract">Contract</Select.Item>
                  <Select.Item value="temporary">Temporary</Select.Item>
                </Select.Content>
              </Select.Root>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="work_arrangement">
              <Form.Label>Work Arrangement <span class="text-destructive">*</span></Form.Label>
              <Select.Root 
                name="work_arrangement" 
                bind:value={$jobForm.work_arrangement}
              >
                <Form.Input>
                  <Select.Trigger class="w-full">
                    <Select.Value placeholder="Select work arrangement" />
                  </Select.Trigger>
                </Form.Input>
                <Select.Content>
                  <Select.Item value="remote">Remote</Select.Item>
                  <Select.Item value="onsite">Onsite</Select.Item>
                  <Select.Item value="hybrid">Hybrid</Select.Item>
                </Select.Content>
              </Select.Root>
              <Form.FieldErrors />
            </Form.Field>
          </div>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field {errors} name="priority">
              <Form.Label>Priority <span class="text-destructive">*</span></Form.Label>
              <Select.Root 
                name="priority" 
                bind:value={$jobForm.priority}
              >
                <Form.Input>
                  <Select.Trigger class="w-full">
                    <Select.Value placeholder="Select priority" />
                  </Select.Trigger>
                </Form.Input>
                <Select.Content>
                  <Select.Item value="A">A - Highest</Select.Item>
                  <Select.Item value="B">B - High</Select.Item>
                  <Select.Item value="C">C - Medium</Select.Item>
                  <Select.Item value="D">D - Low</Select.Item>
                </Select.Content>
              </Select.Root>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="status">
              <Form.Label>Status</Form.Label>
              <Select.Root 
                name="status" 
                bind:value={$jobForm.status}
              >
                <Form.Input>
                  <Select.Trigger class="w-full">
                    <Select.Value placeholder="Select status" />
                  </Select.Trigger>
                </Form.Input>
                <Select.Content>
                  <Select.Item value="draft">Draft</Select.Item>
                  <Select.Item value="open">Open</Select.Item>
                  <Select.Item value="on_hold">On Hold</Select.Item>
                  <Select.Item value="filled">Filled</Select.Item>
                  <Select.Item value="closed">Closed</Select.Item>
                  <Select.Item value="cancelled">Cancelled</Select.Item>
                </Select.Content>
              </Select.Root>
              <Form.FieldErrors />
            </Form.Field>
          </div>
          
          <Form.Field {errors} name="primary_address">
            <Form.Label>Primary Address</Form.Label>
            <Form.Input>
              <Input 
                name="primary_address" 
                bind:value={$jobForm.primary_address} 
                placeholder="Enter job location" 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
          
          <div class="grid grid-cols-2 sm:grid-cols-4 gap-4">
            <Form.Field {errors} name="city">
              <Form.Label>City</Form.Label>
              <Form.Input>
                <Input 
                  name="city" 
                  bind:value={$jobForm.city} 
                  placeholder="City" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="state">
              <Form.Label>State</Form.Label>
              <Form.Input>
                <Input 
                  name="state" 
                  bind:value={$jobForm.state} 
                  placeholder="State" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="country">
              <Form.Label>Country</Form.Label>
              <Form.Input>
                <Input 
                  name="country" 
                  bind:value={$jobForm.country} 
                  placeholder="Country" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="zipcode">
              <Form.Label>Zip Code</Form.Label>
              <Form.Input>
                <Input 
                  name="zipcode" 
                  bind:value={$jobForm.zipcode} 
                  placeholder="Zip Code" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
          </div>
        </Tabs.Content>
        
        <!-- Job Details Tab -->
        <Tabs.Content value="details" class="space-y-4">
          <Form.Field {errors} name="description">
            <Form.Label>Job Description</Form.Label>
            <Form.Input>
              <Textarea 
                name="description" 
                bind:value={$jobForm.description} 
                placeholder="Detailed job description" 
                rows="5" 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
          
          <Form.Field {errors} name="qualifications">
            <Form.Label>Qualifications</Form.Label>
            <Form.Input>
              <Textarea 
                name="qualifications" 
                bind:value={$jobForm.qualifications} 
                placeholder="Required qualifications" 
                rows="3" 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
          
          <Form.Field {errors} name="benefits">
            <Form.Label>Benefits</Form.Label>
            <Form.Input>
              <Textarea 
                name="benefits" 
                bind:value={$jobForm.benefits} 
                placeholder="Job benefits" 
                rows="3" 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field {errors} name="number_of_openings">
              <Form.Label>Number of Openings</Form.Label>
              <Form.Input>
                <Input 
                  name="number_of_openings" 
                  bind:value={$jobForm.number_of_openings} 
                  type="number" 
                  min="1" 
                  placeholder="1" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="experience_level">
              <Form.Label>Experience Level</Form.Label>
              <Select.Root 
                name="experience_level" 
                bind:value={$jobForm.experience_level}
              >
                <Form.Input>
                  <Select.Trigger class="w-full">
                    <Select.Value placeholder="Select experience level" />
                  </Select.Trigger>
                </Form.Input>
                <Select.Content>
                  <Select.Item value="entry">Entry Level</Select.Item>
                  <Select.Item value="mid">Mid Level</Select.Item>
                  <Select.Item value="senior">Senior Level</Select.Item>
                  <Select.Item value="executive">Executive Level</Select.Item>
                </Select.Content>
              </Select.Root>
              <Form.FieldErrors />
            </Form.Field>
          </div>
          
          <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <Form.Field {errors} name="start_date">
              <Form.Label>Start Date</Form.Label>
              <Form.Input>
                <Input 
                  name="start_date" 
                  bind:value={$jobForm.start_date} 
                  type="date" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="end_date">
              <Form.Label>End Date</Form.Label>
              <Form.Input>
                <Input 
                  name="end_date" 
                  bind:value={$jobForm.end_date} 
                  type="date" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="deadline">
              <Form.Label>Application Deadline</Form.Label>
              <Form.Input>
                <Input 
                  name="deadline" 
                  bind:value={$jobForm.deadline} 
                  type="date" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
          </div>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field {errors} name="department">
              <Form.Label>Department</Form.Label>
              <Form.Input>
                <Input 
                  name="department" 
                  bind:value={$jobForm.department} 
                  placeholder="e.g. Engineering" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="internal_notes">
              <Form.Label>Internal Notes</Form.Label>
              <Form.Input>
                <Input 
                  name="internal_notes" 
                  bind:value={$jobForm.internal_notes} 
                  placeholder="Notes visible only to your team" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
          </div>
        </Tabs.Content>
        
        <!-- Compensation Tab -->
        <Tabs.Content value="compensation" class="space-y-4">
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field {errors} name="min_compensation">
              <Form.Label>Minimum Compensation</Form.Label>
              <Form.Input>
                <Input 
                  name="min_compensation" 
                  bind:value={$jobForm.min_compensation} 
                  type="number" 
                  min="0" 
                  placeholder="0" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="max_compensation">
              <Form.Label>Maximum Compensation</Form.Label>
              <Form.Input>
                <Input 
                  name="max_compensation" 
                  bind:value={$jobForm.max_compensation} 
                  type="number" 
                  min="0" 
                  placeholder="0" 
                />
              </Form.Input>
              <Form.FieldErrors />
            </Form.Field>
          </div>
          
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <Form.Field {errors} name="compensation_frequency">
              <Form.Label>Compensation Frequency</Form.Label>
              <Select.Root 
                name="compensation_frequency" 
                bind:value={$jobForm.compensation_frequency}
              >
                <Form.Input>
                  <Select.Trigger class="w-full">
                    <Select.Value placeholder="Select frequency" />
                  </Select.Trigger>
                </Form.Input>
                <Select.Content>
                  <Select.Item value="hourly">Hourly</Select.Item>
                  <Select.Item value="daily">Daily</Select.Item>
                  <Select.Item value="weekly">Weekly</Select.Item>
                  <Select.Item value="monthly">Monthly</Select.Item>
                  <Select.Item value="yearly">Yearly</Select.Item>
                </Select.Content>
              </Select.Root>
              <Form.FieldErrors />
            </Form.Field>
            
            <Form.Field {errors} name="currency">
              <Form.Label>Currency</Form.Label>
              <Select.Root 
                name="currency" 
                bind:value={$jobForm.currency}
              >
                <Form.Input>
                  <Select.Trigger class="w-full">
                    <Select.Value placeholder="Select currency" />
                  </Select.Trigger>
                </Form.Input>
                <Select.Content>
                  <Select.Item value="USD">USD - US Dollar</Select.Item>
                  <Select.Item value="EUR">EUR - Euro</Select.Item>
                  <Select.Item value="GBP">GBP - British Pound</Select.Item>
                  <Select.Item value="CAD">CAD - Canadian Dollar</Select.Item>
                  <Select.Item value="AUD">AUD - Australian Dollar</Select.Item>
                  <Select.Item value="INR">INR - Indian Rupee</Select.Item>
                </Select.Content>
              </Select.Root>
              <Form.FieldErrors />
            </Form.Field>
          </div>
        </Tabs.Content>
      </Tabs.Root>
      
      <Dialog.Footer class="mt-6">
        <Button type="button" variant="outline" on:click={() => handleOpenChange(false)} disabled={$submitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={$submitting}>
          {#if $submitting}
            <Loader2 class="mr-2 h-4 w-4 animate-spin" />
            Creating...
          {:else}
            Create Job
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>

<!-- Client Create Modal -->
<ClientCreateModal 
  bind:open={clientCreateModalOpen} 
  onOpenChange={(open) => clientCreateModalOpen = open}
  onClientCreated={handleClientCreated}
/>