import type { Handle } from '@sveltejs/kit';
import { handlePreClearance } from '$lib/middleware/turnstile';
import { checkRateLimit } from '$lib/middleware/rateLimit.js';
import { logRateLimitEvent } from '$lib/utils/auditLogger.js';

export const handle: Handle = async ({ event, resolve }) => {
  const hostname = event.url.hostname;
  const pathname = event.url.pathname;

  // Get client IP from Cloudflare headers
  const clientIP = event.request.headers.get('cf-connecting-ip') ||
                   event.request.headers.get('x-forwarded-for') ||
                   event.getClientAddress();

  // Skip processing for static assets
  const skipLogging = [
    '/.well-known/',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml',
    '/_app/',
    '/static/'
  ];

  const shouldSkipLogging = skipLogging.some(path => pathname.startsWith(path));

  if (shouldSkipLogging) {
    return resolve(event);
  }

  // Rate limiting for auth endpoints
  if (pathname.startsWith('/api/auth') || pathname.startsWith('/(auth)')) {
    const action = pathname.includes('login') ? 'login' :
                   pathname.includes('register') ? 'register' :
                   pathname.includes('forgot') ? 'password_reset' :
                   'api';

    // Get KV namespace from platform (will be undefined in development)
    const kvNamespace = event.platform?.env?.RATE_LIMITS;
    
    const rateLimit = await checkRateLimit(clientIP, action, undefined, kvNamespace);

    if (!rateLimit.allowed) {
      // Log rate limit exceeded
      await logRateLimitEvent(
        clientIP,
        action,
        clientIP,
        event.request.headers.get('user-agent') || ''
      );

      return new Response(JSON.stringify({
        error: 'Rate limit exceeded',
        resetTime: rateLimit.resetTime,
        blocked: rateLimit.blocked
      }), {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': Math.ceil((rateLimit.resetTime - Date.now()) / 1000).toString()
        }
      });
    }
  }

  // Domain-based routing
  if (hostname === 'sourceflex.io') {
    if (pathname.startsWith('/auth') || pathname.startsWith('/forgot')) {
      return new Response(null, {
        status: 302,
        headers: { Location: `https://app.sourceflex.io${pathname}` }
      });
    }
  }

  // nHost auth integration - check for session token but don't verify server-side
  // Let nHost client handle session validation
  const authHeader = event.request.headers.get('authorization');
  const cookieToken = event.cookies.get('nhost-session');
  const token = authHeader?.replace('Bearer ', '') || cookieToken;

  // Just pass the token info to locals for route protection
  if (token) {
    event.locals.hasToken = true;
  }

  // Handle Turnstile pre-clearance
  const preClearanceResponse = await handlePreClearance(event);
  if (preClearanceResponse) {
    return preClearanceResponse;
  }

  const response = await resolve(event, {
    filterSerializedResponseHeaders: (name) => {
      return name.startsWith('x-') || name === 'content-security-policy';
    }
  });

  // Enhanced security headers for Cloudflare Workers
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');

  // Enhanced CSP for nHost + Turnstile + Cloudflare
  response.headers.set('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://challenges.cloudflare.com https://static.cloudflareinsights.com",
    "style-src 'self' 'unsafe-inline'",
    "connect-src 'self' https://*.nhost.run https://*.graphql.us-west-2.nhost.run https://challenges.cloudflare.com https://cloudflareinsights.com",
    "frame-src https://challenges.cloudflare.com",
    "img-src 'self' data: https:",
    "font-src 'self' https:",
    "worker-src 'self' blob:"
  ].join('; '));

  return response;
};