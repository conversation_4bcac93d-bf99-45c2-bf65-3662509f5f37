import { verifySession } from './lib/auth';

/** @type {import('@sveltejs/kit').Handle} */
export async function handle({ event, resolve }) {
    // Check for session token in cookies
    const sessionToken = event.cookies.get('session_token');
    
    if (sessionToken) {
        const user = await verifySession(sessionToken);
        if (user) {
            event.locals.user = user;
        }
    }
    
    return resolve(event);
}
