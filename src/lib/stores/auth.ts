/**
 * Enhanced nHost authentication store with security features
 * Keeps existing functionality, adds rate limiting and audit logging
 */
import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import { nhost } from './nhost.js';
import type { NhostSession } from '@nhost/nhost-js';
import type { UserProfile, AuthResult, SignUpOptions } from '$lib/types/auth.js';

// Session management flags
let isSigningOut = false;
let authStateListenerActive = true;

// Enhanced session management configuration
const SESSION_CONFIG = {
  IDLE_TIMEOUT: 30 * 60 * 1000, // 30 minutes in milliseconds
  WARNING_TIME: 5 * 60 * 1000,  // Show warning 5 minutes before timeout
  ACTIVITY_EVENTS: ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
};

// Session state stores
export const session = writable<NhostSession | null>(null);
export const userProfile = writable<UserProfile | null>(null);
export const sessionWarning = writable<boolean>(false);
export const sessionTimeRemaining = writable<number>(SESSION_CONFIG.IDLE_TIMEOUT);

// Derived stores
export const isAuthenticated = derived(session, ($session) => {
  return $session?.user && !$session?.user?.isAnonymous;
});

export const userRole = derived(session, ($session) => {
  if (!$session?.user) return null;
  return $session.user.defaultRole || 'user';
});

export const userRoles = derived(session, ($session) => {
  if (!$session?.user) return [];
  const roles = $session.user.roles || [];
  return roles;
});

// Session management class (kept from original)
class SessionManager {
  private idleTimer: number | null = null;
  private warningTimer: number | null = null;
  private lastActivity: number = Date.now();
  private warningShown: boolean = false;

  constructor() {
    if (!browser) return;
    this.setupActivityListeners();
    this.startTimers();
  }

  private setupActivityListeners() {
    if (!browser || typeof document === 'undefined') return;
    SESSION_CONFIG.ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, () => this.updateActivity(), { passive: true });
    });
  }

  private updateActivity() {
    this.lastActivity = Date.now();
    if (this.warningShown) {
      this.hideWarning();
    }
    this.resetTimers();
  }

  private startTimers() {
    this.resetTimers();
  }

  private resetTimers() {
    if (this.idleTimer) clearTimeout(this.idleTimer);
    if (this.warningTimer) clearTimeout(this.warningTimer);
    
    this.warningTimer = window.setTimeout(() => {
      this.showWarning();
    }, SESSION_CONFIG.IDLE_TIMEOUT - SESSION_CONFIG.WARNING_TIME);
    
    this.idleTimer = window.setTimeout(() => {
      this.handleIdleTimeout();
    }, SESSION_CONFIG.IDLE_TIMEOUT);
    
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
  }

  private showWarning() {
    this.warningShown = true;
    sessionWarning.set(true);
    
    const startTime = Date.now();
    const countdownInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = SESSION_CONFIG.WARNING_TIME - elapsed;
      
      if (remaining <= 0) {
        clearInterval(countdownInterval);
        return;
      }
      
      sessionTimeRemaining.set(remaining);
    }, 1000);
  }

  private hideWarning() {
    this.warningShown = false;
    sessionWarning.set(false);
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
  }

  private async handleIdleTimeout() {
    console.log('Session idle timeout - signing out');
    await authActions.signOut();
  }

  public extendSession() {
    this.updateActivity();
  }

  public destroy() {
    if (!browser) return;
    if (this.idleTimer) clearTimeout(this.idleTimer);
    if (this.warningTimer) clearTimeout(this.warningTimer);
    
    if (typeof document !== 'undefined') {
      SESSION_CONFIG.ACTIVITY_EVENTS.forEach(event => {
        document.removeEventListener(event, () => this.updateActivity());
      });
    }
  }
}

// Initialize session manager
let sessionManager: SessionManager | null = null;

// Enhanced auth actions with security logging
export const authActions = {
  signUp: async (email: string, password: string, options: SignUpOptions = {}): Promise<AuthResult> => {
    console.log('🔄 Starting signUp with enhanced security');
    
    // Log registration attempt
    await logAuthAttempt('register', email, true);
    
    try {
      const result = await nhost.auth.signUp(
        {
          email,
          password,
          options: {
            redirectTo: '/welcome',
            ...options
          }
        }
      );
      
      // Log result
      await logAuthAttempt('register', email, !!result.session, result.error?.message);
      
      return result as AuthResult;
    } catch (error) {
      return { error, session: null } as AuthResult;
    }
  },

  signIn: async (email: string, password: string): Promise<AuthResult> => {
    console.log('🔄 Starting signIn with enhanced security');
    
    // Log login attempt
    await logAuthAttempt('login', email, true);
    
    try {
      const result = await nhost.auth.signIn({
        email,
        password
      });

      // Log result and handle success
      if (result.session?.user) {
        const sessionToken = result.session?.accessToken || result.session?.refreshToken;
        await logAuthAttempt('login', email, true, undefined, sessionToken);
        console.log('User signed in successfully');
      } else {
        await logAuthAttempt('login', email, false, result.error?.message);
      }

      return result as AuthResult;
    } catch (authError) {
      await logAuthAttempt('login', email, false, (authError as Error)?.toString());
      return { error: authError, session: null } as AuthResult;
    }
  },

  signOut: async (): Promise<AuthResult> => {
    console.log('🚪 Enhanced signOut with security logging');
    
    // Log logout attempt
    const currentUser = nhost.auth.getUser();
    const currentSession = nhost.auth.getSession();
    if (currentUser?.email) {
      const sessionToken = currentSession?.accessToken || currentSession?.refreshToken;
      await logAuthAttempt('logout', currentUser.email, true, undefined, sessionToken);
    }
    
    if (isSigningOut) {
      return { error: null, session: null } as AuthResult;
    }
    
    isSigningOut = true;
    
    try {
      // Clean up session manager
      if (sessionManager) {
        sessionManager.destroy();
        sessionManager = null;
      }
      
      // Clear stores
      session.set(null);
      userProfile.set(null);
      sessionWarning.set(false);
      sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
      
      // Standard nHost signOut
      const result = await nhost.auth.signOut();
      
      if (browser) {
        // Clear browser storage
        localStorage.clear();
        sessionStorage.clear();
        
        // Redirect
        window.location.href = '/';
      }
      
      return result as AuthResult;
    } catch (error) {
      console.error('SignOut error:', error);
      return { error, session: null } as AuthResult;
    } finally {
      isSigningOut = false;
    }
  },

  // Keep all your existing methods with enhanced logging
  extendSession: () => {
    if (sessionManager) {
      sessionManager.extendSession();
    }
  },

  sendPasswordResetEmail: async (email: string): Promise<AuthResult> => {
    await logAuthAttempt('password_reset', email, true);
    
    try {
      const result = await nhost.auth.resetPassword({ 
        email,
        options: {
          redirectTo: '/profile/settings?action=change-password'
        }
      });
      
      await logAuthAttempt('password_reset', email, !result.error, result.error?.message);
      return result as AuthResult;
    } catch (error) {
      await logAuthAttempt('password_reset', email, false, (error as Error)?.toString());
      return { session: null, error } as AuthResult;
    }
  },

  resetPassword: async (password: string, passwordResetToken: string): Promise<AuthResult> => {
    try {
      const response = await fetch(`${nhost.auth.url}/user/password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          newPassword: password,
          ticket: passwordResetToken
        })
      });
      
      if (response.ok) {
        const data = await response.json();
        return { session: data.session || null, error: null } as AuthResult;
      } else {
        return { 
          session: null, 
          error: { 
            message: 'Password reset link has expired. Please request a new reset link.',
            code: 'TOKEN_EXPIRED'
          } 
        } as AuthResult;
      }
    } catch (error) {
      return { 
        session: null, 
        error: error instanceof Error ? error : { 
          message: 'Password reset link has expired. Please request a new reset link.',
          code: 'TOKEN_EXPIRED'
        } 
      } as AuthResult;
    }
  }
};

// Enhanced logging function
async function logAuthAttempt(action: string, email: string, success: boolean, errorMessage?: string, sessionToken?: string) {
  try {
    const response = await fetch('/api/auth/log', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action,
        userId: email, // Use email as identifier for logging
        success,
        details: { errorMessage },
        sessionToken // Pass session token for session tracking
      })
    });
    
    if (!response.ok) {
      console.error('❌ Audit log API failed:', response.status, response.statusText);
    }
    } catch (error) {
      console.error('❌ Auth logging failed:', error);
    }
}

// Browser initialization (keep your existing logic)
if (browser) {
  const initializeAuth = async () => {
    try {
      console.log('🔄 INITIALIZING ENHANCED AUTH SYSTEM...');
      
      // Clean initialization
      isSigningOut = false;
      authStateListenerActive = true;
      session.set(null);
      userProfile.set(null);
      
      // Check for existing session
      const initialSession = nhost.auth.getSession();
      
      if (initialSession?.user) {
        console.log('✅ Found existing session for:', initialSession.user.email);
        session.set(initialSession);
        sessionManager = new SessionManager();
      } else {
        console.log('✅ No initial session found - starting fresh');
      }
      
      console.log('✅ Enhanced auth initialization completed');
    } catch (error) {
      console.error('❌ Auth initialization error:', error);
      session.set(null);
      userProfile.set(null);
    }
  };

  // Listen to auth state changes (keep your existing logic)
  nhost.auth.onAuthStateChanged((event: string, nhostSession: NhostSession | null) => {
    if (isSigningOut || !authStateListenerActive) {
      return;
    }
    
    console.log('Auth state changed:', event, 'User:', nhostSession?.user?.email);
    
    if (event === 'SIGNED_IN' && nhostSession?.user) {
      session.set(nhostSession);
      if (!sessionManager) {
        sessionManager = new SessionManager();
      }
    } else if (event === 'SIGNED_OUT' || !nhostSession?.user) {
      if (!isSigningOut) {
        session.set(null);
        userProfile.set(null);
        sessionWarning.set(false);
        sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
        
        if (sessionManager) {
          sessionManager.destroy();
          sessionManager = null;
        }
      }
    }
  });

  initializeAuth();
}

// Export session configuration for components
export { SESSION_CONFIG };