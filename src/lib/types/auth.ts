// User Profile and Application-specific types
export interface UserProfile {
  id: string;
  user_id: string;
  current_desk: 'recruitment' | 'bench_sales';
  current_organization_id?: string;
  email_domain: string;
  business_name?: string;
  timezone: string;
  onboarding_completed: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface OrgUser extends UserProfile {
  user: {
    email: string;
    displayName?: string;
  };
}

export interface AdminUser extends UserProfile {
  user: {
    email: string;
    displayName?: string;
    defaultRole: string;
    roles: Array<{ role: string }>;
  };
}

// Auth action results
export interface AuthResult {
  session?: {
    user?: {
      id?: string;
      email?: string;
      emailVerified?: boolean;
    };
    accessToken?: string;
    refreshToken?: string;
  } | null;
  user?: {
    id?: string;
    email?: string;
    emailVerified?: boolean;
    defaultRole?: string;
    roles?: string[];
  } | null;
  error?: {
    message: string;
    status?: number;
    code?: string;
  } | null;
}

// Sign up options
export interface SignUpOptions {
  metadata?: {
    business_name?: string | null;
    email_domain?: string;
  };
  redirectTo?: string;
}
