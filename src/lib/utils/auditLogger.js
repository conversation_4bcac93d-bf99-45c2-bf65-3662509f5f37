/**
 * Audit Logger Utility
 * Provides logging functionality for rate limiting and security events
 */

/**
 * Log a rate limit event
 * @param {string} action - The action that triggered the rate limit
 * @param {string} identifier - The identifier (IP, user ID, etc.)
 * @param {Object} details - Additional details about the event
 */
export async function logRateLimitEvent(action, identifier, details = {}) {
  try {
    // In development, just log to console
    if (process.env.NODE_ENV === 'development') {
      console.log('🚦 Rate Limit Event:', {
        action,
        identifier,
        details,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // In production, you would send this to your audit logging service
    // For now, we'll just log to console
    console.log('🚦 Rate Limit Event:', {
      action,
      identifier,
      details,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Failed to log rate limit event:', error);
  }
}

/**
 * Log a security event
 * @param {string} event - The security event type
 * @param {string} identifier - The identifier (IP, user ID, etc.)
 * @param {Object} details - Additional details about the event
 */
export async function logSecurityEvent(event, identifier, details = {}) {
  try {
    // In development, just log to console
    if (process.env.NODE_ENV === 'development') {
      console.log('🔒 Security Event:', {
        event,
        identifier,
        details,
        timestamp: new Date().toISOString()
      });
      return;
    }

    // In production, you would send this to your audit logging service
    // For now, we'll just log to console
    console.log('🔒 Security Event:', {
      event,
      identifier,
      details,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('❌ Failed to log security event:', error);
  }
}
