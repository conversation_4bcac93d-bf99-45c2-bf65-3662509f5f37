/**
 * Modern timezone utilities using browser APIs
 * Built for SourceFlex - uses Intl.supportedValuesOf for comprehensive timezone support
 */

export interface ModernTimezoneOption {
	value: string; // IANA timezone identifier
	label: string; // Human-readable label
	region: string; // Continent/Region
	country?: string; // Country name
	city: string; // Main city
	offset: string; // Current UTC offset (e.g., "+05:30")
	abbreviation: string; // Current timezone abbreviation (e.g., "EST")
	searchTerms: string; // Searchable terms
}

/**
 * Get all supported timezones using modern browser API
 */
export function getAllSupportedTimezones(): string[] {
	try {
		// Use modern browser API if available
		if ('supportedValuesOf' in Intl) {
			return Intl.supportedValuesOf('timeZone');
		}
		
		// Fallback to common timezones
		return [
			'UTC',
			'America/New_York',
			'America/Chicago', 
			'America/Denver',
			'America/Los_Angeles',
			'Europe/London',
			'Europe/Paris',
			'Europe/Berlin',
			'Asia/Kolkata',
			'Asia/Dubai',
			'Asia/Shanghai',
			'Asia/Tokyo',
			'Australia/Sydney',
			'Pacific/Auckland'
		];
	} catch {
		// Ultimate fallback
		return ['UTC', 'America/New_York', 'Europe/London', 'Asia/Kolkata'];
	}
}

/**
 * Create timezone option from IANA identifier
 */
export function createTimezoneOption(ianaId: string): ModernTimezoneOption {
	try {
		const now = new Date();
		
		// Get current offset
		const formatter = new Intl.DateTimeFormat('en', {
			timeZone: ianaId,
			timeZoneName: 'longOffset'
		});
		const parts = formatter.formatToParts(now);
		const offsetPart = parts.find(part => part.type === 'timeZoneName');
		const offset = offsetPart?.value || '+00:00';
		
		// Get abbreviation
		const abbrFormatter = new Intl.DateTimeFormat('en', {
			timeZone: ianaId,
			timeZoneName: 'short'
		});
		const abbrParts = abbrFormatter.formatToParts(now);
		const abbrPart = abbrParts.find(part => part.type === 'timeZoneName');
		const abbreviation = abbrPart?.value || '';
		
		// Parse timezone ID
		const parts_tz = ianaId.split('/');
		const region = parts_tz[0] || '';
		const city = parts_tz[parts_tz.length - 1]?.replace(/_/g, ' ') || '';
		
		// Create human-readable label
		const label = `${abbreviation} - ${city}${region !== city ? ` (${region})` : ''} ${offset}`;
		
		// Generate search terms
		const searchTerms = [
			abbreviation.toLowerCase(),
			city.toLowerCase(),
			region.toLowerCase(),
			ianaId.toLowerCase(),
			offset.replace(':', ''),
			// Additional common search terms
			...(getCommonSearchTerms(ianaId))
		].join(' ');
		
		return {
			value: ianaId,
			label,
			region,
			city,
			offset,
			abbreviation,
			searchTerms
		};
	} catch (error) {
		console.warn(`Failed to create timezone option for ${ianaId}:`, error);
		return {
			value: ianaId,
			label: ianaId,
			region: '',
			city: ianaId,
			offset: '+00:00',
			abbreviation: '',
			searchTerms: ianaId.toLowerCase()
		};
	}
}

/**
 * Get common search terms for popular timezones
 */
function getCommonSearchTerms(ianaId: string): string[] {
	const commonTerms: Record<string, string[]> = {
		'America/New_York': ['est', 'edt', 'eastern', 'usa', 'boston', 'washington'],
		'America/Chicago': ['cst', 'cdt', 'central', 'usa', 'texas', 'dallas'],
		'America/Denver': ['mst', 'mdt', 'mountain', 'usa', 'colorado'],
		'America/Los_Angeles': ['pst', 'pdt', 'pacific', 'usa', 'california', 'san francisco'],
		'Europe/London': ['gmt', 'bst', 'uk', 'britain', 'england'],
		'Europe/Paris': ['cet', 'cest', 'france', 'central europe'],
		'Europe/Berlin': ['cet', 'cest', 'germany', 'central europe'],
		'Asia/Kolkata': ['ist', 'india', 'mumbai', 'delhi', 'bangalore', 'chennai'],
		'Asia/Dubai': ['gst', 'gulf', 'uae', 'emirates'],
		'Asia/Shanghai': ['cst', 'china', 'beijing', 'hong kong'],
		'Asia/Tokyo': ['jst', 'japan'],
		'Australia/Sydney': ['aest', 'aedt', 'australia', 'melbourne'],
		'UTC': ['utc', 'gmt', 'coordinated', 'universal', 'greenwich']
	};
	
	return commonTerms[ianaId] || [];
}

/**
 * Get prioritized timezone list (popular timezones first)
 */
export function getPrioritizedTimezones(): ModernTimezoneOption[] {
	// Popular timezones that should appear first
	const popularTimezones = [
		'UTC',
		'America/New_York',
		'America/Chicago',
		'America/Denver', 
		'America/Los_Angeles',
		'Europe/London',
		'Europe/Paris',
		'Europe/Berlin',
		'Asia/Kolkata',
		'Asia/Dubai',
		'Asia/Shanghai',
		'Asia/Tokyo',
		'Asia/Seoul',
		'Asia/Singapore',
		'Australia/Sydney',
		'Pacific/Auckland'
	];
	
	const allTimezones = getAllSupportedTimezones();
	const remaining = allTimezones.filter(tz => !popularTimezones.includes(tz));
	
	// Combine popular + remaining, create options
	const orderedTimezones = [...popularTimezones, ...remaining.sort()];
	
	return orderedTimezones.map(createTimezoneOption);
}

/**
 * Auto-detect user's timezone
 */
export function detectUserTimezone(): string {
	try {
		return Intl.DateTimeFormat().resolvedOptions().timeZone;
	} catch {
		return 'UTC';
	}
}

/**
 * Search timezones by query
 */
export function searchModernTimezones(query: string, timezones: ModernTimezoneOption[]): ModernTimezoneOption[] {
	if (!query.trim()) return timezones;
	
	const searchQuery = query.toLowerCase().trim();
	
	return timezones.filter(tz => 
		tz.searchTerms.includes(searchQuery) ||
		tz.label.toLowerCase().includes(searchQuery) ||
		tz.city.toLowerCase().includes(searchQuery) ||
		tz.abbreviation.toLowerCase().includes(searchQuery)
	);
}

/**
 * Format timezone with current time
 */
export function formatTimezoneWithCurrentTime(timezone: string): string {
	try {
		const now = new Date();
		const timeString = now.toLocaleTimeString('en-US', {
			timeZone: timezone,
			hour: '2-digit',
			minute: '2-digit',
			hour12: true
		});
		
		const option = createTimezoneOption(timezone);
		return `${option.label} - ${timeString}`;
	} catch {
		return timezone;
	}
}
