export interface TimezoneOption {
	value: string;
	label: string;
	searchTerms: string;
}

export const TIMEZONES: TimezoneOption[] = [
	// Major World Timezones (24 most commonly used)
	{
		value: "UTC",
		label: "UTC - Coordinated Universal Time",
		searchTerms: "utc coordinated universal time gmt greenwich"
	},
	{
		value: "America/New_York",
		label: "EST/EDT - Eastern Time (US)",
		searchTerms: "est edt eastern time us usa america new york boston washington"
	},
	{
		value: "America/Chicago",
		label: "CST/CDT - Central Time (US)",
		searchTerms: "cst cdt central time us usa america chicago texas dallas houston"
	},
	{
		value: "America/Denver",
		label: "MST/MDT - Mountain Time (US)",
		searchTerms: "mst mdt mountain time us usa america denver colorado utah"
	},
	{
		value: "America/Los_Angeles",
		label: "PST/PDT - Pacific Time (US)",
		searchTerms: "pst pdt pacific time us usa america los angeles california san francisco"
	},
	{
		value: "Europe/London",
		label: "GMT/BST - London, UK",
		searchTerms: "gmt bst london uk united kingdom britain england"
	},
	{
		value: "Europe/Paris",
		label: "CET/CEST - Central Europe",
		searchTerms: "cet cest central european time paris france germany berlin amsterdam"
	},
	{
		value: "Europe/Rome",
		label: "CET/CEST - Italy",
		searchTerms: "cet cest italy italian rome milan naples"
	},
	{
		value: "Europe/Madrid",
		label: "CET/CEST - Spain",
		searchTerms: "cet cest spain spanish madrid barcelona"
	},
	{
		value: "Europe/Moscow",
		label: "MSK - Moscow, Russia",
		searchTerms: "msk moscow russia russian"
	},
	{
		value: "Asia/Kolkata",
		label: "IST - India Standard Time",
		searchTerms: "ist india indian kolkata delhi mumbai bangalore chennai hyderabad pune"
	},
	{
		value: "Asia/Dubai",
		label: "GST - Gulf Standard Time",
		searchTerms: "gst gulf dubai uae emirates abu dhabi middle east"
	},
	{
		value: "Asia/Shanghai",
		label: "CST - China Standard Time",
		searchTerms: "cst china chinese beijing shanghai hong kong"
	},
	{
		value: "Asia/Tokyo",
		label: "JST - Japan Standard Time",
		searchTerms: "jst japan japanese tokyo osaka"
	},
	{
		value: "Asia/Seoul",
		label: "KST - Korea Standard Time",
		searchTerms: "kst korea korean seoul south korea"
	},
	{
		value: "Asia/Singapore",
		label: "SGT - Singapore Time",
		searchTerms: "sgt singapore malaysian kuala lumpur"
	},
	{
		value: "Asia/Bangkok",
		label: "ICT - Indochina Time",
		searchTerms: "ict thailand thai bangkok vietnam ho chi minh"
	},
	{
		value: "Australia/Sydney",
		label: "AEST/AEDT - Australian Eastern",
		searchTerms: "aest aedt australia australian sydney melbourne brisbane"
	},
	{
		value: "Australia/Perth",
		label: "AWST - Australian Western",
		searchTerms: "awst australia australian perth western"
	},
	{
		value: "Pacific/Auckland",
		label: "NZST/NZDT - New Zealand",
		searchTerms: "nzst nzdt new zealand auckland wellington"
	},
	{
		value: "America/Sao_Paulo",
		label: "BRT - Brazil Time",
		searchTerms: "brt brazil brazilian sao paulo rio janeiro"
	},
	{
		value: "America/Mexico_City",
		label: "CST/CDT - Mexico Central",
		searchTerms: "cst cdt mexico mexican mexico city"
	},
	{
		value: "America/Toronto",
		label: "EST/EDT - Canada Eastern",
		searchTerms: "est edt canada canadian toronto ottawa montreal"
	},
	{
		value: "Africa/Cairo",
		label: "EET - Egypt Time",
		searchTerms: "eet egypt egyptian cairo alexandria"
	}
];

/**
 * Auto-detect user's timezone using browser API
 */
export function detectTimezone(): string {
	try {
		const detected = Intl.DateTimeFormat().resolvedOptions().timeZone;
		// Check if detected timezone is in our list
		const found = TIMEZONES.find(tz => tz.value === detected);
		return found ? detected : "America/New_York"; // Default to Eastern if not found
	} catch {
		// Fallback to Eastern Time if detection fails
		return "America/New_York";
	}
}

/**
 * Format timezone for display with current time
 */
export function formatTimezoneWithTime(timezone: string): string {
	try {
		const now = new Date();
		const timeString = now.toLocaleTimeString('en-US', {
			timeZone: timezone,
			hour: '2-digit',
			minute: '2-digit',
			hour12: true
		});
		
		const timezoneOption = TIMEZONES.find(tz => tz.value === timezone);
		const label = timezoneOption?.label || timezone;
		
		return `${label} - ${timeString}`;
	} catch {
		return timezone;
	}
}

/**
 * Get timezone option by value
 */
export function getTimezoneOption(value: string): TimezoneOption | undefined {
	return TIMEZONES.find(tz => tz.value === value);
}

/**
 * Search timezones by query
 */
export function searchTimezones(query: string): TimezoneOption[] {
	if (!query) return TIMEZONES;
	
	const searchQuery = query.toLowerCase().trim();
	return TIMEZONES.filter(tz => 
		tz.searchTerms.toLowerCase().includes(searchQuery)
	);
}
