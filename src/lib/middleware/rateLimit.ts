/**
 * Cloudflare KV-based rate limiting for SourceFlex
 * Optimized for Cloudflare Workers environment
 */

interface RateLimitEntry {
    count: number;
    resetTime: number;
    blocked: boolean;
}

export interface RateLimitConfig {
    maxAttempts: number;
    windowMs: number;
    blockDurationMs?: number;
}

export interface RateLimitResult {
    allowed: boolean;
    remaining: number;
    resetTime: number;
    blocked: boolean;
}

// Default rate limit configurations
const defaultConfigs: Record<string, RateLimitConfig> = {
    login: { 
        maxAttempts: 5, 
        windowMs: 15 * 60 * 1000, // 15 minutes
        blockDurationMs: 30 * 60 * 1000 // 30 minutes block
    },
    register: { 
        maxAttempts: 3, 
        windowMs: 60 * 60 * 1000 // 1 hour
    },
    password_reset: { 
        maxAttempts: 3, 
        windowMs: 60 * 60 * 1000 // 1 hour
    },
    api: { 
        maxAttempts: 100, 
        windowMs: 60 * 1000 // 1 minute
    }
};

/**
 * Check rate limit using Cloudflare KV
 */
export async function checkRateLimit(
    identifier: string,
    action: string,
    config?: RateLimitConfig,
    kvNamespace?: KVNamespace
): Promise<RateLimitResult> {
    
    const cfg = config || defaultConfigs[action] || defaultConfigs.api;
    const key = `ratelimit:${action}:${identifier}`;
    const now = Date.now();
    
    // Fallback to in-memory if KV not available (development)
    if (!kvNamespace) {
        return checkRateLimitInMemory(identifier, action, cfg);
    }
    
    try {
        // Get current entry from KV
        const entryJson = await kvNamespace.get(key);
        let entry: RateLimitEntry | null = null;
        
        if (entryJson) {
            try {
                entry = JSON.parse(entryJson);
            } catch (e) {
                console.error('Failed to parse rate limit entry:', e);
                entry = null;
            }
        }
        
        // No entry or expired window
        if (!entry || now > entry.resetTime) {
            const newEntry: RateLimitEntry = {
                count: 1,
                resetTime: now + cfg.windowMs,
                blocked: false
            };
            
            // Store in KV with TTL
            await kvNamespace.put(
                key, 
                JSON.stringify(newEntry),
                { expirationTtl: Math.ceil(cfg.windowMs / 1000) + 60 } // Add 60s buffer
            );
            
            return {
                allowed: true,
                remaining: cfg.maxAttempts - 1,
                resetTime: newEntry.resetTime,
                blocked: false
            };
        }
        
        // Check if blocked
        if (entry.blocked && cfg.blockDurationMs) {
            const blockUntil = entry.resetTime + cfg.blockDurationMs;
            if (now < blockUntil) {
                return {
                    allowed: false,
                    remaining: 0,
                    resetTime: blockUntil,
                    blocked: true
                };
            } else {
                // Block period expired, reset
                const newEntry: RateLimitEntry = {
                    count: 1,
                    resetTime: now + cfg.windowMs,
                    blocked: false
                };
                
                await kvNamespace.put(
                    key, 
                    JSON.stringify(newEntry),
                    { expirationTtl: Math.ceil(cfg.windowMs / 1000) + 60 }
                );
                
                return {
                    allowed: true,
                    remaining: cfg.maxAttempts - 1,
                    resetTime: newEntry.resetTime,
                    blocked: false
                };
            }
        }
        
        // Increment count
        entry.count++;
        
        // Check if limit exceeded
        if (entry.count > cfg.maxAttempts) {
            entry.blocked = true;
            
            await kvNamespace.put(
                key, 
                JSON.stringify(entry),
                { expirationTtl: Math.ceil((cfg.windowMs + (cfg.blockDurationMs || 0)) / 1000) + 60 }
            );
            
            return {
                allowed: false,
                remaining: 0,
                resetTime: entry.resetTime,
                blocked: true
            };
        }
        
        // Update entry
        await kvNamespace.put(
            key, 
            JSON.stringify(entry),
            { expirationTtl: Math.ceil(cfg.windowMs / 1000) + 60 }
        );
        
        return {
            allowed: true,
            remaining: cfg.maxAttempts - entry.count,
            resetTime: entry.resetTime,
            blocked: false
        };
        
    } catch (error) {
        console.error('KV rate limit check failed:', error);
        // Fallback to allowing request on KV failure
        return {
            allowed: true,
            remaining: cfg.maxAttempts - 1,
            resetTime: now + cfg.windowMs,
            blocked: false
        };
    }
}

/**
 * Reset rate limit for a specific identifier/action
 */
export async function resetRateLimit(
    identifier: string, 
    action: string,
    kvNamespace?: KVNamespace
): Promise<void> {
    const key = `ratelimit:${action}:${identifier}`;
    
    if (kvNamespace) {
        try {
            await kvNamespace.delete(key);
        } catch (error) {
            console.error('Failed to reset rate limit:', error);
        }
    } else {
        // Fallback to in-memory reset
        resetRateLimitInMemory(identifier, action);
    }
}

// Fallback in-memory rate limiting for development
const memoryStore = new Map<string, RateLimitEntry>();

function checkRateLimitInMemory(
    identifier: string,
    action: string,
    config: RateLimitConfig
): RateLimitResult {
    const key = `${action}:${identifier}`;
    const now = Date.now();
    const entry = memoryStore.get(key);
    
    // No entry or expired window
    if (!entry || now > entry.resetTime) {
        memoryStore.set(key, {
            count: 1,
            resetTime: now + config.windowMs,
            blocked: false
        });
        
        return {
            allowed: true,
            remaining: config.maxAttempts - 1,
            resetTime: now + config.windowMs,
            blocked: false
        };
    }
    
    // Check if blocked
    if (entry.blocked && config.blockDurationMs) {
        const blockUntil = entry.resetTime + config.blockDurationMs;
        if (now < blockUntil) {
            return {
                allowed: false,
                remaining: 0,
                resetTime: blockUntil,
                blocked: true
            };
        } else {
            // Block period expired, reset
            memoryStore.set(key, {
                count: 1,
                resetTime: now + config.windowMs,
                blocked: false
            });
            return {
                allowed: true,
                remaining: config.maxAttempts - 1,
                resetTime: now + config.windowMs,
                blocked: false
            };
        }
    }
    
    // Increment count
    entry.count++;
    
    // Check if limit exceeded
    if (entry.count > config.maxAttempts) {
        entry.blocked = true;
        return {
            allowed: false,
            remaining: 0,
            resetTime: entry.resetTime,
            blocked: true
        };
    }
    
    return {
        allowed: true,
        remaining: config.maxAttempts - entry.count,
        resetTime: entry.resetTime,
        blocked: false
    };
}

function resetRateLimitInMemory(identifier: string, action: string): void {
    const key = `${action}:${identifier}`;
    memoryStore.delete(key);
}