import type { RequestEvent } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';

/**
 * Pre-clearance Turnstile middleware for enterprise-grade authentication flow
 * 
 * This middleware implements Cloudflare's pre-clearance pattern where users
 * verify once per session rather than on every auth page.
 */

export interface TurnstileResponse {
  success: boolean;
  'error-codes'?: string[];
  challenge_ts?: string;
  hostname?: string;
  action?: string;
  cdata?: string;
}

/**
 * Get Turnstile secret key with proper fallback for Cloudflare Workers 2025+
 */
function getTurnstileSecretKey(): string | undefined {
  // In Cloudflare Workers, prioritize env from $env/dynamic/private
  if (env.TURNSTILE_SECRET_KEY) {
    return env.TURNSTILE_SECRET_KEY;
  }
  
  // Fallback for Node.js environments (local dev)
  if (typeof process !== 'undefined' && process.env?.TURNSTILE_SECRET_KEY) {
    return process.env.TURNSTILE_SECRET_KEY;
  }
  
  // Additional fallback for older compatibility
  if (typeof globalThis !== 'undefined' && globalThis.process?.env?.TURNSTILE_SECRET_KEY) {
    return globalThis.process.env.TURNSTILE_SECRET_KEY;
  }
  
  return undefined;
}

/**
 * Verifies Turnstile token with Cloudflare's siteverify API
 */
export async function verifyTurnstileToken(
  token: string, 
  remoteip?: string
): Promise<TurnstileResponse> {
  const TURNSTILE_SECRET_KEY = getTurnstileSecretKey();
  
  if (!TURNSTILE_SECRET_KEY) {
    console.error('TURNSTILE_SECRET_KEY not found in environment variables');
    return {
      success: false,
      'error-codes': ['missing-secret-key']
    };
  }
  
  // Handle test keys in development
  if (TURNSTILE_SECRET_KEY === '1x0000000000000000000000000000000AA') {
    console.log('Using test secret key - always returning success');
    return {
      success: true,
      challenge_ts: new Date().toISOString(),
      hostname: 'localhost'
    };
  }

  const formData = new FormData();
  formData.append('secret', TURNSTILE_SECRET_KEY);
  formData.append('response', token);
  
  if (remoteip) {
    formData.append('remoteip', remoteip);
  }

  try {
    console.log('Verifying Turnstile token with Cloudflare...');
    const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Turnstile API error: ${response.status}`);
    }

    const result = await response.json();
    console.log('Turnstile verification result:', result);
    return result;
  } catch (error) {
    console.error('Turnstile verification failed:', error);
    return {
      success: false,
      'error-codes': ['network-error']
    };
  }
}

/**
 * Checks if user has valid pre-clearance (cf_clearance cookie OR turnstile_session)
 */
export function hasClearanceCookie(event: RequestEvent): boolean {
  const clearanceCookie = event.cookies.get('cf_clearance');
  const sessionCookie = event.cookies.get('turnstile_session');
  
  // Accept either cloudflare clearance cookie OR our session cookie
  return !!(clearanceCookie || sessionCookie === 'verified');
}

/**
 * Checks if the current route requires Turnstile verification
 */
export function requiresTurnstileVerification(pathname: string): boolean {
  // Auth routes that need pre-clearance protection
  const protectedRoutes = [
    '/', // Root auth page
    '/forgotpassword', 
    '/resetpassword',
    '/resendverification',
    '/verify'
  ];

  return protectedRoutes.some(route => pathname.startsWith(route));
}

/**
 * Middleware function to handle pre-clearance verification
 */
export async function handlePreClearance(event: RequestEvent): Promise<Response | null> {
  const { url } = event;
  
  // Skip verification for non-protected routes
  if (!requiresTurnstileVerification(url.pathname)) {
    return null;
  }

  // In development, log the flow for debugging
  if (env.NODE_ENV === 'development') {
    console.log('Pre-clearance check for:', url.pathname);
    console.log('Has clearance cookie:', hasClearanceCookie(event));
  }

  // Check if user already has clearance cookie
  if (hasClearanceCookie(event)) {
    console.log('User has clearance, allowing access');
    return null; // Allow access
  }

  // If accessing protected route without clearance, redirect to verification
  if (url.pathname !== '/auth/verify-human') {
    console.log('Redirecting to verification page');
    const redirectUrl = new URL('/auth/verify-human', url.origin);
    redirectUrl.searchParams.set('return', url.pathname + url.search);
    
    return Response.redirect(redirectUrl.toString(), 302);
  }

  return null;
}

/**
 * Sets pre-clearance session state after successful verification
 */
export function setClearanceSession(event: RequestEvent): void {
  // Set a session cookie to track clearance state
  // Note: The actual cf_clearance cookie is set by Cloudflare's pre-clearance widget
  event.cookies.set('turnstile_session', 'verified', {
    path: '/',
    httpOnly: true,
    secure: false, // Allow HTTP for localhost development
    sameSite: 'lax', // Less strict for development
    maxAge: 60 * 60 * 2 // 2 hours
  });
}

/**
 * Validates the current session clearance state
 */
export function validateClearanceSession(event: RequestEvent): boolean {
  const sessionCookie = event.cookies.get('turnstile_session');
  const clearanceCookie = event.cookies.get('cf_clearance');
  
  // Both session and clearance cookies should be present
  return sessionCookie === 'verified' && !!clearanceCookie;
}
