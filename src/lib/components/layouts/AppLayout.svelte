<script lang="ts">
  import { onMount } from 'svelte';
  import AppSidebar from '$lib/components/ui/AppSidebar.svelte';
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';
  import { SIDEBAR_COOKIE_NAME } from '$lib/components/ui/sidebar/constants.js';

  interface AppLayoutProps {
    children?: any;
  }

  let { children }: AppLayoutProps = $props();
  let sidebarOpen = $state(false); // Default to collapsed on desktop

  // Load sidebar state from cookie on mount
  onMount(() => {
    const cookies = document.cookie.split('; ');
    const sidebarCookie = cookies.find(cookie => cookie.startsWith(`${SIDEBAR_COOKIE_NAME}=`));
    if (sidebarCookie) {
      const cookieValue = sidebarCookie.split('=')[1];
      sidebarOpen = cookieValue === 'true';
    }
  });
</script>

<Sidebar.Provider bind:open={sidebarOpen}>
  <AppSidebar />
  <Sidebar.Inset>
    <!-- Mobile Header with Hamburger Menu -->
    <header class="md:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center gap-3">
      <Sidebar.Trigger class="flex h-9 w-9 items-center justify-center rounded-md hover:bg-gray-100 transition-colors" />
      <div class="flex items-center gap-2">
        <div class="flex h-8 w-8 items-center justify-center rounded-lg bg-blue-600 text-white">
          <span class="text-xs font-bold">SF</span>
        </div>
        <span class="font-bold text-gray-900">SourceFlex</span>
      </div>
    </header>
    
    <main class="flex-1 overflow-auto p-6 transition-opacity duration-300 ease-out">
      {@render children?.()}
    </main>
  </Sidebar.Inset>
</Sidebar.Provider>
