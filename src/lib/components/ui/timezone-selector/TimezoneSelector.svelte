<!--
Modern Timezone Selector using Select component with enhanced search
Built for SourceFlex - mobile-optimized and reusable across the app
-->
<script lang="ts">
	import * as Select from "$lib/components/ui/select";
	import { 
		getPrioritizedTimezones, 
		searchModernTimezones, 
		detectUserTimezone,
		formatTimezoneWithCurrentTime,
		type ModernTimezoneOption 
	} from "$lib/utils/timezone/modern-timezones";
	import { Input } from "$lib/components/ui/input";
	import { Clock } from "lucide-svelte";

	interface Props {
		value?: string;
		onValueChange?: (value: string) => void;
		placeholder?: string;
		class?: string;
	}

	let { 
		value = $bindable(), 
		onValueChange, 
		placeholder = "Select timezone...",
		class: className = ""
	}: Props = $props();

	// Get all timezones and initialize
	const allTimezones = getPrioritizedTimezones();
	let filteredTimezones = $state(allTimezones);
	let searchQuery = $state('');

	// Auto-detect timezone if not provided
	if (!value) {
		value = detectUserTimezone();
	}

	// Get selected timezone option
	const selectedTimezone = $derived(
		allTimezones.find(tz => tz.value === value)
	);

	// Filter timezones based on search
	$effect(() => {
		if (searchQuery.trim()) {
			filteredTimezones = searchModernTimezones(searchQuery, allTimezones);
		} else {
			filteredTimezones = allTimezones;
		}
	});

	function handleValueChange(newValue: string | undefined) {
		if (newValue) {
			value = newValue;
			onValueChange?.(newValue);
		}
	}

	function handleSearchInput(event: Event) {
		const target = event.target as HTMLInputElement;
		searchQuery = target.value;
	}
</script>

<div class="space-y-2 {className}">
	<Select.Root 
		type="single"
		bind:value
		onValueChange={handleValueChange}
	>
		<Select.Trigger class="w-full">
			<div class="flex items-center gap-2 text-left">
				<Clock class="h-4 w-4 text-muted-foreground flex-shrink-0" />
				<span class="flex-1 truncate">
					{#if selectedTimezone}
						{selectedTimezone.label}
					{:else}
						{placeholder}
					{/if}
				</span>
			</div>
		</Select.Trigger>
		
		<Select.Content class="max-h-80">
			<!-- Search input -->
			<div class="p-2 border-b sticky top-0 bg-background">
				<Input
					type="text"
					placeholder="Search timezones (e.g., 'EST', 'India', 'UTC')..."
					value={searchQuery}
					oninput={handleSearchInput}
					class="h-8"
				/>
			</div>
			
			<!-- Timezone options -->
			<div class="max-h-64 overflow-y-auto">
				{#each filteredTimezones as timezone (timezone.value)}
					<Select.Item value={timezone.value}>
						<div class="flex flex-col gap-1 py-1">
							<div class="flex items-center justify-between gap-2">
								<span class="font-medium text-sm">{timezone.abbreviation}</span>
								<span class="text-xs text-muted-foreground">{timezone.offset}</span>
							</div>
							<div class="text-xs text-muted-foreground">
								{timezone.city}{timezone.region !== timezone.city ? ` (${timezone.region})` : ''}
							</div>
						</div>
					</Select.Item>
				{:else}
					<div class="p-4 text-sm text-muted-foreground text-center">
						No timezones found for "{searchQuery}"
					</div>
				{/each}
			</div>
		</Select.Content>
	</Select.Root>

	<!-- Current time display -->
	{#if value && selectedTimezone}
		<div class="p-3 bg-muted rounded-lg">
			<div class="text-sm font-medium">Current time:</div>
			<div class="text-sm text-muted-foreground">
				{formatTimezoneWithCurrentTime(value)}
			</div>
		</div>
	{/if}
</div>
