<!--
Simple Radio Group for Desk Selection - Corporate/Professional Design
-->
<script lang="ts">
	import { Label } from "$lib/components/ui/label";
	
	type DeskType = 'recruitment' | 'bench_sales';

	interface Props {
		value?: DeskType;
		onValueChange?: (value: DeskType) => void;
		class?: string;
	}

	let { 
		value = $bindable(), 
		onValueChange,
		class: className = ""
	}: Props = $props();

	function handleChange(event: Event) {
		const target = event.target as HTMLInputElement;
		const newValue = target.value as DeskType;
		value = newValue;
		onValueChange?.(newValue);
	}
</script>

<div class="space-y-3 {className}">
	<div class="flex flex-col sm:flex-row sm:space-x-6 space-y-3 sm:space-y-0">
		<div class="flex items-center space-x-2">
			<input 
				type="radio" 
				id="recruitment" 
				name="desk-selection"
				value="recruitment"
				checked={value === 'recruitment'}
				onchange={handleChange}
				class="h-4 w-4 text-primary focus:ring-primary focus:ring-2 border-gray-300"
			/>
			<Label for="recruitment" class="text-sm font-medium leading-none">
				Recruitment
			</Label>
		</div>
		
		<div class="flex items-center space-x-2">
			<input 
				type="radio" 
				id="bench_sales" 
				name="desk-selection"
				value="bench_sales"
				checked={value === 'bench_sales'}
				onchange={handleChange}
				class="h-4 w-4 text-primary focus:ring-primary focus:ring-2 border-gray-300"
			/>
			<Label for="bench_sales" class="text-sm font-medium leading-none">
				Bench Sales
			</Label>
		</div>
	</div>
</div>
