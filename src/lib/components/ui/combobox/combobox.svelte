<script lang="ts">
	import CheckIcon from "lucide-svelte/icons/check";
	import ChevronsUpDownIcon from "lucide-svelte/icons/chevrons-up-down";
	import { tick } from "svelte";
	import * as Command from "$lib/components/ui/command/index.js";
	import * as Popover from "$lib/components/ui/popover/index.js";
	import { Button } from "$lib/components/ui/button/index.js";
	import { cn } from "$lib/utils.js";

	interface ComboboxOption {
		value: string;
		label: string;
		searchTerms?: string;
	}

	interface Props {
		options: ComboboxOption[];
		value?: string;
		placeholder?: string;
		searchPlaceholder?: string;
		emptyMessage?: string;
		class?: string;
		onSelect?: (value: string) => void;
	}

	let {
		options,
		value = $bindable(""),
		placeholder = "Select an option...",
		searchPlaceholder = "Search...",
		emptyMessage = "No option found.",
		class: className = "",
		onSelect
	}: Props = $props();

	let open = $state(false);
	let triggerRef = $state<HTMLButtonElement>(null!);
	let searchQuery = $state("");

	const selectedValue = $derived(
		options.find((option) => option.value === value)?.label
	);

	// Filter options based on search query
	const filteredOptions = $derived(() => {
		if (!searchQuery.trim()) return options;
		
		const query = searchQuery.toLowerCase();
		return options.filter(option => {
			const searchIn = `${option.label} ${option.searchTerms || ""}`.toLowerCase();
			return searchIn.includes(query);
		});
	});

	// We want to refocus the trigger button when the user selects
	// an item from the list so users can continue navigating the
	// rest of the form with the keyboard.
	function closeAndFocusTrigger() {
		open = false;
		searchQuery = ""; // Reset search when closing
		tick().then(() => {
			triggerRef.focus();
		});
	}

	function handleSelect(optionValue: string) {
		value = optionValue;
		onSelect?.(optionValue);
		closeAndFocusTrigger();
	}

	function handleOpenChange(newOpen: boolean) {
		open = newOpen;
		if (!newOpen) {
			searchQuery = ""; // Reset search when closing
		}
	}
</script>

<Popover.Root bind:open onOpenChange={handleOpenChange}>
	<Popover.Trigger bind:ref={triggerRef}>
		{#snippet child({ props })}
			<Button
				variant="outline"
				class={cn("w-full justify-between", className)}
				{...props}
				role="combobox"
				aria-expanded={open}
			>
				<span class="truncate">
					{selectedValue || placeholder}
				</span>
				<ChevronsUpDownIcon class="ml-2 h-4 w-4 shrink-0 opacity-50" />
			</Button>
		{/snippet}
	</Popover.Trigger>
	<Popover.Content class="w-full p-0" style="width: var(--bits-popover-trigger-width)">
		<Command.Root shouldFilter={false}>
			<Command.Input 
				bind:value={searchQuery}
				placeholder={searchPlaceholder} 
				class="h-9" 
			/>
			<Command.List>
				{#if filteredOptions.length === 0}
					<Command.Empty>{emptyMessage}</Command.Empty>
				{:else}
					<Command.Group>
						{#each filteredOptions as option (option.value)}
							<Command.Item
								value={option.value}
								onSelect={() => handleSelect(option.value)}
							>
								<CheckIcon
									class={cn(
										"mr-2 h-4 w-4",
										value !== option.value && "text-transparent"
									)}
								/>
								<span class="truncate">{option.label}</span>
							</Command.Item>
						{/each}
					</Command.Group>
				{/if}
			</Command.List>
		</Command.Root>
	</Popover.Content>
</Popover.Root>
