<script lang="ts">
	import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "$lib/components/ui/card";
	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import { TimezoneSelector } from "$lib/components/ui/timezone-selector";
	import { DeskSelector } from "$lib/components/ui/desk-selector";
	import { detectUserTimezone } from "$lib/utils/timezone/modern-timezones";
	import { createEventDispatcher } from 'svelte';

	interface OnboardingData {
		firstName: string;
		lastName: string;
		jobTitle: string;
		businessName: string;
		phoneNumber: string;
		currentDesk: 'recruitment' | 'bench_sales';
		timezone: string;
	}

	interface Props {
		formData: OnboardingData;
		isLoading?: boolean;
	}

	let { formData = $bindable(), isLoading = false }: Props = $props();

	const dispatch = createEventDispatcher<{
		submit: OnboardingData;
	}>();

	// Auto-detect timezone on mount
	let detectedTimezone = $state(detectUserTimezone());
	
	// Initialize timezone if not set
	if (!formData.timezone) {
		formData.timezone = detectedTimezone;
	}

	// Form validation
	const isFormValid = $derived(
		formData.firstName.trim().length >= 2 &&
		formData.lastName.trim().length >= 2 &&
		formData.jobTitle.trim().length >= 2 &&
		formData.businessName.trim().length >= 2 &&
		formData.currentDesk !== null &&
		formData.timezone.length > 0
	);

	function handleSubmit() {
		if (isFormValid) {
			dispatch('submit', formData);
		}
	}

	function handleTimezoneChange(newTimezone: string) {
		formData.timezone = newTimezone;
	}

	function handleDeskChange(newDesk: 'recruitment' | 'bench_sales') {
		formData.currentDesk = newDesk;
	}
</script>

<div class="max-w-2xl mx-auto px-4">
	<Card>
		<CardHeader class="text-center space-y-2">
			<CardTitle class="text-xl sm:text-2xl font-bold">Complete Your Profile</CardTitle>
			<p class="text-sm sm:text-base text-muted-foreground">
				Set up your account to get started with SourceFlex
			</p>
		</CardHeader>
		
		<CardContent class="space-y-6">
			<!-- Personal Information -->
			<div class="space-y-4">
				<h3 class="text-lg font-semibold">Personal Information</h3>
				
				<div class="space-y-4">
					<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<div class="space-y-2">
							<Label for="firstName">First Name</Label>
							<Input 
								id="firstName" 
								bind:value={formData.firstName}
								required
								placeholder="Enter your first name"
							/>
						</div>
						<div class="space-y-2">
							<Label for="lastName">Last Name</Label>
							<Input 
								id="lastName" 
								bind:value={formData.lastName}
								required
								placeholder="Enter your last name"
							/>
						</div>
					</div>

					<div class="space-y-2">
						<Label for="jobTitle">Job Title</Label>
						<Input 
							id="jobTitle" 
							bind:value={formData.jobTitle}
							required
							placeholder="e.g., Senior Recruiter"
						/>
					</div>
					
					<div class="space-y-2">
						<Label for="phoneNumber">Phone Number <span class="text-muted-foreground">(optional)</span></Label>
						<Input 
							id="phoneNumber" 
							bind:value={formData.phoneNumber}
							type="tel"
							placeholder="+****************"
						/>
					</div>
				</div>
			</div>

			<!-- Business Information -->
			<div class="space-y-4">
				<h3 class="text-lg font-semibold">Business Details</h3>
				
				<div class="space-y-2">
					<Label for="businessName">Business Name</Label>
					<Input 
						id="businessName" 
						bind:value={formData.businessName}
						required
						placeholder="Enter your company name"
					/>
				</div>

				<div class="space-y-3">
					<Label>Primary Desk</Label>
					<DeskSelector 
						bind:value={formData.currentDesk}
						onValueChange={handleDeskChange}
					/>
				</div>
			</div>

			<!-- Timezone Selection -->
			<div class="space-y-4">
				<h3 class="text-lg font-semibold">Timezone</h3>
				<p class="text-sm text-muted-foreground">
					We've detected your timezone. You can change it if needed.
				</p>
				
				<TimezoneSelector 
					bind:value={formData.timezone}
					onValueChange={handleTimezoneChange}
					placeholder="Select your timezone..."
				/>
			</div>

			<!-- Submit Button -->
			<div class="pt-4">
				<Button 
					onclick={handleSubmit}
					disabled={!isFormValid || isLoading}
					class="w-full h-12 text-base font-medium"
					size="lg"
				>
					{isLoading ? 'Completing Setup...' : 'Complete Setup'}
				</Button>
			</div>
		</CardContent>
	</Card>
</div>
