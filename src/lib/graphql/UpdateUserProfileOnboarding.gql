# Update user profile for onboarding completion
mutation UpdateUserProfileOnboarding(
  $userId: uuid!
  $firstName: String!
  $lastName: String!
  $jobTitle: String!
  $businessName: String!
  $phoneNumber: String
  $currentDesk: desk_type!
  $timezone: String!
) {
  update_user_profiles(
    where: { user_id: { _eq: $userId } }
    _set: {
      first_name: $firstName
      last_name: $lastName
      job_title: $jobTitle
      business_name: $businessName
      phone_number: $phoneNumber
      current_desk: $currentDesk
      timezone: $timezone
      onboarding_completed: true
    }
  ) {
    affected_rows
    returning {
      id
      first_name
      last_name
      job_title
      business_name
      phone_number
      current_desk
      timezone
      onboarding_completed
      updated_at
    }
  }
}
