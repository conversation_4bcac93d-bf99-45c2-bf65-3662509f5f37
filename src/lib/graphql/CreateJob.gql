# Create a new job posting
mutation CreateJob($job: jobs_insert_input!) {
  insert_jobs_one(object: $job) {
    id
    title
    status
    priority
    job_type
    work_arrangement
    experience_level
    description
    city
    state
    country
    min_compensation
    max_compensation
    compensation_frequency
    currency
    created_at
    updated_at
    client_id
    client {
      id
      name
      industry
    }
    creator {
      id
      displayName
      email
    }
  }
}
