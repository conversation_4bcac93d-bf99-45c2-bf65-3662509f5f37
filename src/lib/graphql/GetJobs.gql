# Get jobs with pagination and filtering
query GetJobs($limit: Int = 50, $offset: Int = 0, $where: jobs_bool_exp) {
  jobs(
    limit: $limit
    offset: $offset
    where: $where
    order_by: { created_at: desc }
  ) {
    id
    title
    status
    priority
    job_type
    work_arrangement
    experience_level
    description
    city
    state
    country
    min_compensation
    max_compensation
    compensation_frequency
    currency
    created_at
    updated_at
    client_id
    client {
      id
      name
      industry
      website
    }
    creator {
      id
      displayName
      email
    }
  }
}
