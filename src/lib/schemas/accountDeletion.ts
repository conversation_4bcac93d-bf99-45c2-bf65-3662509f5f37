import { z } from 'zod';

export const accountDeletionSchema = z.object({
	reason: z.string().optional(),
	confirmText: z.string().min(1, 'Please type DELETE to confirm').refine(
		(val) => val === 'DELETE',
		'You must type DELETE exactly to confirm'
	),
	acknowledgment: z.boolean().refine(
		(val) => val === true,
		'You must acknowledge that this action is permanent'
	)
});

export type AccountDeletionSchema = z.infer<typeof accountDeletionSchema>;
