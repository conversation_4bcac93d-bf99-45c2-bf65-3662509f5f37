/**
 * Client-side authentication utilities
 * Safe to import in stores and components
 */

/**
 * Call server-side API to terminate other sessions
 * This is a client-side wrapper for the server function
 */
export async function terminateOtherSessionsClient(userId: string, accessToken: string): Promise<boolean> {
    try {
        const response = await fetch('/api/auth/terminate-sessions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                userId,
                currentToken: accessToken
            })
        });

        const result = await response.json();
        return result.success || false;
    } catch (error) {
        console.error('Failed to terminate other sessions:', error);
        return false;
    }
}