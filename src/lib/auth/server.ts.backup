/**
 * Server-side authentication utilities for Cloudflare Workers + nHost
 * This file should only be imported in server-side code (hooks, API routes, etc.)
 */
import { env } from '$env/dynamic/private';

interface User {
    id: string;
    email: string;
    defaultRole: string;
    roles: string[];
    emailVerified: boolean;
}

/**
 * Verify nHost JWT token and validate session (SERVER-SIDE ONLY)
 */
export async function verifySession(token: string): Promise<User | null> {
    try {
        // Get nHost GraphQL endpoint
        const NHOST_SUBDOMAIN = env.PUBLIC_NHOST_SUBDOMAIN || 'pttthnqikxdsxmeccqho';
        const NHOST_REGION = env.PUBLIC_NHOST_REGION || 'us-west-2';
        const graphqlUrl = `https://${NHOST_SUBDOMAIN}.graphql.${NHOST_REGION}.nhost.run/v1`;
        
        // Verify token by making authenticated request
        const response = await fetch(graphqlUrl, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: `
                    query GetCurrentUser {
                        users {
                            id
                            email
                            defaultRole
                            roles {
                                role
                            }
                            emailVerified
                        }
                    }
                `
            })
        });
        
        if (!response.ok) {
            console.error('Token verification failed:', response.status);
            return null;
        }
        
        const data = await response.json();
        
        if (data.errors) {
            console.error('GraphQL errors:', data.errors);
            return null;
        }
        
        const user = data.data?.users?.[0];
        if (!user) {
            return null;
        }
        
        // Transform roles array
        const roles = user.roles?.map((r: any) => r.role) || [];
        
        return {
            id: user.id,
            email: user.email,
            defaultRole: user.defaultRole || 'user',
            roles: roles,
            emailVerified: user.emailVerified
        };
        
    } catch (error) {
        console.error('Session verification failed:', error);
        return null;
    }
}

/**
 * Terminate all other active sessions for a user (SERVER-SIDE ONLY)
 */
export async function terminateOtherSessions(userId: string, currentSessionToken: string): Promise<boolean> {
    try {
        const NHOST_SUBDOMAIN = env.PUBLIC_NHOST_SUBDOMAIN || 'pttthnqikxdsxmeccqho';
        const NHOST_REGION = env.PUBLIC_NHOST_REGION || 'us-west-2';
        const graphqlUrl = `https://${NHOST_SUBDOMAIN}.graphql.${NHOST_REGION}.nhost.run/v1`;
        
        await fetch(graphqlUrl, {
            method: 'POST',
            headers: {
                'x-hasura-admin-secret': env.NHOST_ADMIN_SECRET || '',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: `
                    mutation TerminateOtherSessions($userId: uuid!, $currentToken: String!) {
                        update_user_sessions(
                            where: {
                                user_id: {_eq: $userId}
                                session_token: {_neq: $currentToken}
                                is_active: {_eq: true}
                            }
                            _set: {is_active: false}
                        ) {
                            affected_rows
                        }
                    }
                `,
                variables: { 
                    userId,
                    currentToken: currentSessionToken
                }
            })
        });
        
        return true;
    } catch (error) {
        console.error('Failed to terminate other sessions:', error);
        return false;
    }
}

/**
 * Clean up expired sessions (SERVER-SIDE ONLY)
 */
export async function cleanupExpiredSessions(): Promise<void> {
    try {
        const NHOST_SUBDOMAIN = env.PUBLIC_NHOST_SUBDOMAIN || 'pttthnqikxdsxmeccqho';
        const NHOST_REGION = env.PUBLIC_NHOST_REGION || 'us-west-2';
        const graphqlUrl = `https://${NHOST_SUBDOMAIN}.graphql.${NHOST_REGION}.nhost.run/v1`;
        
        await fetch(graphqlUrl, {
            method: 'POST',
            headers: {
                'x-hasura-admin-secret': env.NHOST_ADMIN_SECRET || '',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: `
                    mutation CleanupExpiredSessions {
                        update_user_sessions(
                            where: {
                                expires_at: {_lt: "now()"}
                                is_active: {_eq: true}
                            }
                            _set: {is_active: false}
                        ) {
                            affected_rows
                        }
                    }
                `
            })
        });
    } catch (error) {
        console.error('Session cleanup failed:', error);
    }
}