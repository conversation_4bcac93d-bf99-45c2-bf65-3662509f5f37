import type { LayoutServerLoad } from './$types';

export const load: LayoutServerLoad = async ({ url }) => {
  // Enhanced nHost integration - let client-side handle all auth logic
  // Server-side only provides URL info and security context
  
  return {
    url: url.pathname,
    // Add any global server-side data here
    securityHeaders: {
      rateLimitingActive: true,
      auditLoggingEnabled: true
    }
  };
};