import { json } from '@sveltejs/kit';
import { terminateOtherSessions } from '$lib/auth/server.js';
import type { RequestHandler } from './$types';

export const POST: RequestHandler = async ({ request }) => {
    try {
        const { userId, currentToken } = await request.json();
        
        if (!userId || !currentToken) {
            return json({ error: 'Missing required parameters' }, { status: 400 });
        }
        
        const success = await terminateOtherSessions(userId, currentToken);
        
        return json({ success });
    } catch (error) {
        console.error('Terminate sessions API error:', error);
        return json({ error: 'Failed to terminate sessions' }, { status: 500 });
    }
};