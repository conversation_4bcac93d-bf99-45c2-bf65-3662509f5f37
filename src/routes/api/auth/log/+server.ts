/**
 * Secure Audit Logging API Endpoint
 * Production-ready with proper authentication, validation, and error handling
 */
import { json } from '@sveltejs/kit';
import { env } from '$env/dynamic/private';
import type { RequestHand<PERSON> } from './$types';

// Security constants
const MAX_REQUEST_SIZE = 1024; // 1KB limit for audit logs
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10;

// Rate limiting storage (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Input validation schemas
interface AuditRequest {
  action: string;
  userId: string;
  success: boolean;
  details?: Record<string, any>;
  sessionToken?: string;
}

interface SessionData {
  userId: string;
  sessionToken: string;
  ipAddress: string;
  userAgent: string;
  expiresAt: string;
}

/**
 * Validate audit request input
 */
function validateAuditRequest(data: any): AuditRequest | null {
  if (!data || typeof data !== 'object') return null;
  
  // Required fields validation
  if (!data.action || typeof data.action !== 'string') return null;
  if (!data.userId || typeof data.userId !== 'string') return null;
  if (typeof data.success !== 'boolean') return null;
  
  // Sanitize action (only allow specific audit actions)
  const allowedActions = ['login', 'logout', 'register', 'password_reset', 'failed_login'];
  if (!allowedActions.includes(data.action.toLowerCase())) return null;
  
  // Validate email format for userId
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(data.userId)) return null;
  
  return {
    action: data.action.toLowerCase(),
    userId: data.userId.toLowerCase().trim(),
    success: data.success,
    details: data.details && typeof data.details === 'object' ? data.details : {},
    sessionToken: data.sessionToken && typeof data.sessionToken === 'string' ? data.sessionToken : undefined
  };
}

/**
 * Rate limiting check
 */
function checkRateLimit(clientIP: string): boolean {
  const now = Date.now();
  const key = `audit_${clientIP}`;
  const current = rateLimitStore.get(key);
  
  if (!current || now > current.resetTime) {
    rateLimitStore.set(key, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }
  
  if (current.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false;
  }
  
  current.count++;
  return true;
}

/**
 * Secure GraphQL request with proper error handling and fallback environment loading
 */
async function executeSecureGraphQL(query: string, variables: any): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    // Primary environment variable loading
    let NHOST_SUBDOMAIN = env.PUBLIC_NHOST_SUBDOMAIN;
    let NHOST_REGION = env.PUBLIC_NHOST_REGION;
    let NHOST_ADMIN_SECRET = env.NHOST_ADMIN_SECRET;
    
    // Fallback to process.env for SvelteKit environment loading issues
    if (!NHOST_SUBDOMAIN) NHOST_SUBDOMAIN = process.env.PUBLIC_NHOST_SUBDOMAIN;
    if (!NHOST_REGION) NHOST_REGION = process.env.PUBLIC_NHOST_REGION;
    if (!NHOST_ADMIN_SECRET) NHOST_ADMIN_SECRET = process.env.NHOST_ADMIN_SECRET;
    
    // Final validation with specific error logging
    if (!NHOST_SUBDOMAIN) {
      console.error('❌ PUBLIC_NHOST_SUBDOMAIN not found in environment variables');
      return { success: false, error: 'Configuration error: missing subdomain' };
    }
    if (!NHOST_REGION) {
      console.error('❌ PUBLIC_NHOST_REGION not found in environment variables');
      return { success: false, error: 'Configuration error: missing region' };
    }
    if (!NHOST_ADMIN_SECRET) {
      console.error('❌ NHOST_ADMIN_SECRET not found in environment variables');
      return { success: false, error: 'Configuration error: missing admin secret' };
    }
    
    // Only log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ Environment variables loaded for audit logging');
    }
    
    const graphqlUrl = `https://${NHOST_SUBDOMAIN}.graphql.${NHOST_REGION}.nhost.run/v1`;
    
    const response = await fetch(graphqlUrl, {
      method: 'POST',
      headers: {
        'x-hasura-admin-secret': NHOST_ADMIN_SECRET,
        'Content-Type': 'application/json',
        'User-Agent': 'SourceFlex-Audit/1.0'
      },
      body: JSON.stringify({ query, variables }),
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });
    
    if (!response.ok) {
      console.error(`❌ GraphQL request failed: ${response.status} ${response.statusText}`);
      return { success: false, error: 'Database connection failed' };
    }
    
    const result = await response.json();
    
    if (result.errors) {
      console.error('❌ GraphQL errors:', result.errors);
      return { success: false, error: 'Database query failed' };
    }
    
    return { success: true, data: result.data };
    
  } catch (error) {
    console.error('❌ Secure GraphQL execution failed:', error);
    return { success: false, error: 'Internal error' };
  }
}

/**
 * Get user ID by email securely
 */
async function getUserIdByEmail(email: string): Promise<string | null> {
  const result = await executeSecureGraphQL(
    `query GetUserByEmail($email: citext!) {
      users(where: {email: {_eq: $email}}) {
        id
      }
    }`,
    { email }
  );
  
  if (result.success && result.data?.users?.length > 0) {
    return result.data.users[0].id;
  }
  
  return null;
}

/**
 * Create audit log entry securely
 */
async function createAuditLog(auditData: AuditRequest, clientIP: string, userAgent: string): Promise<boolean> {
  try {
    // Look up the actual user ID from email
    const userId = await getUserIdByEmail(auditData.userId);
    
    // Create audit log with proper user_id
    const result = await executeSecureGraphQL(
      `mutation CreateAuditLog($input: audit_logs_insert_input!) {
        insert_audit_logs_one(object: $input) {
          id
        }
      }`,
      {
        input: {
          user_id: userId, // Store actual user ID if found, null for non-existent users
          action: `AUTH_${auditData.action.toUpperCase()}`,
          resource: 'authentication',
          resource_id: null,
          details: {
            user_email: auditData.userId, // Keep email in details for backup
            user_id_resolved: userId !== null, // Flag indicating if user lookup succeeded
            ...auditData.details
          },
          ip_address: clientIP,
          user_agent: userAgent,
          success: auditData.success,
          error_message: auditData.details?.errorMessage || null
        }
      }
    );

    return result?.data?.insert_audit_logs_one?.id ? true : false;
  } catch (error) {
    console.error('Failed to create audit log:', error);
    return false;
  }
}

/**
 * Create user session entry securely
 */
async function createUserSession(sessionData: SessionData): Promise<boolean> {
  const result = await executeSecureGraphQL(
    `mutation CreateUserSession($input: user_sessions_insert_input!) {
      insert_user_sessions_one(object: $input) {
        id
      }
    }`,
    {
      input: {
        user_id: sessionData.userId,
        session_token: sessionData.sessionToken,
        ip_address: sessionData.ipAddress,
        user_agent: sessionData.userAgent,
        expires_at: sessionData.expiresAt,
        is_active: true
      }
    }
  );
  
  return result.success;
}

/**
 * Deactivate user session securely
 */
async function deactivateUserSession(sessionToken: string): Promise<boolean> {
  const result = await executeSecureGraphQL(
    `mutation DeactivateSession($sessionToken: String!) {
      update_user_sessions(
        where: {
          session_token: {_eq: $sessionToken}
          is_active: {_eq: true}
        }
        _set: {is_active: false}
      ) {
        affected_rows
      }
    }`,
    { sessionToken }
  );
  
  return result.success;
}

/**
 * Main API endpoint handler
 */
export const POST: RequestHandler = async ({ request, getClientAddress }) => {
  try {
    // Get client information
    const clientIP = request.headers.get('cf-connecting-ip') || 
                     request.headers.get('x-forwarded-for') || 
                     getClientAddress();
    const userAgent = request.headers.get('user-agent') || 'Unknown';
    
    // Rate limiting
    if (!checkRateLimit(clientIP)) {
      return json({ error: 'Rate limit exceeded' }, { status: 429 });
    }
    
    // Request size validation
    const contentLength = request.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > MAX_REQUEST_SIZE) {
      return json({ error: 'Request too large' }, { status: 413 });
    }
    
    // Parse and validate request
    const rawData = await request.json();
    const auditData = validateAuditRequest(rawData);
    
    if (!auditData) {
      return json({ error: 'Invalid request data' }, { status: 400 });
    }
    
    // Create audit log (always do this first)
    const auditSuccess = await createAuditLog(auditData, clientIP, userAgent);
    
    // Handle session tracking for successful login/logout
    if (auditData.success && auditData.sessionToken) {
      if (auditData.action === 'login') {
        const userId = await getUserIdByEmail(auditData.userId);
        if (userId) {
          const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(); // 7 days
          await createUserSession({
            userId,
            sessionToken: auditData.sessionToken,
            ipAddress: clientIP,
            userAgent,
            expiresAt
          });
        }
      } else if (auditData.action === 'logout') {
        await deactivateUserSession(auditData.sessionToken);
      }
    }
    
    return json({ 
      success: true,
      auditLogged: auditSuccess
    });
    
  } catch (error) {
    console.error('❌ Secure audit API error:', error);
    return json({ 
      error: 'Internal server error',
      success: false 
    }, { status: 500 });
  }
};
