<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';

  let turnstileContainer: HTMLDivElement;
  let widgetId: string | null = null;
  let isLoaded = false;
  let token = '';
  let verificationResult = '';
  let isVerifying = false;

  const SITE_KEY = import.meta.env.PUBLIC_TURNSTILE_SITE_KEY;
  const returnUrl = $page.url.searchParams.get('return') || '/dashboard';

  const loadTurnstile = () => {
    return new Promise<void>((resolve, reject) => {
      if (typeof window.turnstile !== 'undefined') {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
      script.async = true;
      script.defer = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Turnstile script'));
      document.head.appendChild(script);
    });
  };

  const renderWidget = () => {
    if (!turnstileContainer || !window.turnstile) return;

    try {
      widgetId = window.turnstile.render(turnstileContainer, {
        sitekey: SITE_KEY,
        callback: handleSuccess,
        'error-callback': handleError,
        'expired-callback': handleExpired,
        theme: 'light',
        size: 'normal'
      });
      isLoaded = true;
    } catch (error) {
      console.error('Failed to render Turnstile widget:', error);
    }
  };

  const handleSuccess = async (token: string) => {
    console.log('Turnstile verification successful, token:', token);
    isVerifying = true;
    
    try {
      // Send token to server for verification
      const response = await fetch('/api/auth/verify-clearance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token })
      });

      const result = await response.json();
      
      if (result.success) {
        verificationResult = 'Verification successful! Redirecting...';
        // Redirect to the return URL or dashboard
        setTimeout(() => {
          goto(returnUrl);
        }, 1000);
      } else {
        verificationResult = 'Verification failed. Please try again.';
        resetWidget();
      }
    } catch (error) {
      console.error('Verification request failed:', error);
      verificationResult = 'Network error. Please try again.';
      resetWidget();
    } finally {
      isVerifying = false;
    }
  };

  const handleError = (error: string) => {
    console.error('Turnstile error:', error);
    verificationResult = 'Verification error. Please refresh and try again.';
  };

  const handleExpired = () => {
    console.log('Turnstile token expired');
    verificationResult = 'Verification expired. Please try again.';
    resetWidget();
  };

  const resetWidget = () => {
    if (widgetId && window.turnstile) {
      window.turnstile.reset(widgetId);
    }
    token = '';
    verificationResult = '';
  };

  onMount(async () => {
    if (!browser) return;

    try {
      await loadTurnstile();
      renderWidget();
    } catch (error) {
      console.error('Failed to initialize Turnstile:', error);
      verificationResult = 'Failed to load verification widget. Please refresh the page.';
    }
  });
</script>

<svelte:head>
  <title>Human Verification - {import.meta.env.PUBLIC_APP_NAME || 'SourceFlex'}</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Human Verification Required
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Please complete the verification below to continue
      </p>
    </div>

    <div class="bg-white shadow rounded-lg p-6">
      {#if !SITE_KEY}
        <div class="text-red-600 text-center">
          <p>Turnstile site key not configured</p>
        </div>
      {:else}
        <div class="space-y-4">
          <div class="flex justify-center">
            <div bind:this={turnstileContainer}></div>
          </div>

          {#if isVerifying}
            <div class="text-center">
              <div class="inline-flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Verifying...
              </div>
            </div>
          {/if}

          {#if verificationResult}
            <div class="text-center">
              <p class={verificationResult.includes('successful') ? 'text-green-600' : 'text-red-600'}>
                {verificationResult}
              </p>
            </div>
          {/if}

          {#if !isLoaded && !verificationResult}
            <div class="text-center text-gray-500">
              Loading verification widget...
            </div>
          {/if}
        </div>
      {/if}
    </div>

    <div class="text-center">
      <p class="text-xs text-gray-500">
        This verification helps protect against automated attacks
      </p>
    </div>
  </div>
</div>

<style>
  /* Ensure Turnstile widget is properly centered */
  :global(.cf-turnstile) {
    margin: 0 auto;
  }
</style>
