<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import { nhost } from '$lib/stores/nhost.js';
  import { session, authActions } from '$lib/stores/auth.js';
  import toast from 'svelte-5-french-toast';
  import { Card, CardContent, CardHeader } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Textarea } from '$lib/components/ui/textarea';
  import { Eye, EyeOff, CheckCircle, X, Trash2, AlertTriangle } from 'lucide-svelte';

  let newPassword = $state('');
  let confirmPassword = $state('');
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let isLoading = $state(false);
  let showChangePassword = $state(false);
  let passwordChangeSuccess = $state(false);
  let countdownSeconds = $state(5);
  let countdownInterval: any = $state(null);
  let signOutTimeout: any = $state(null);
  let showDeleteDialog = $state(false);
  let deleteForm = $state({
    reason: '',
    confirmText: '',
    acknowledgment: false
  });
  let isDeleting = $state(false);

  /**
   * Enhanced password validation for nHost (same as RegisterForm)
   * @param {string} password
   * @returns {{ isValid: boolean; errors: string[] }}
   */
  function validatePassword(password: string) {
    /** @type {string[]} */
    const errors = [];

    if (password.length < 8) {
      errors.push('At least 8 characters');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('One lowercase letter');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('One uppercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('One number');
    }

    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(password)) {
      errors.push('One special character');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  onMount(() => {
    // ProtectedRoute will handle authentication
    // Just check for password reset action
    const action = $page.url.searchParams.get('action');
    if (action === 'change-password') {
      showChangePassword = true;
      toast.success('You have been signed in. Please set your new password below.');
    }
  });

  async function handlePasswordChange(event: Event) {
    event.preventDefault();
    
    if (!newPassword || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (newPassword !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    // Enhanced password validation using the same logic as registration
    const passwordValidation = validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      toast.error(`Password must contain: ${passwordValidation.errors.join(', ')}`);
      return;
    }

    isLoading = true;

    try {
      const result = await nhost.auth.changePassword({ newPassword });
      
      if (result.error) {
        console.error('❌ Password change failed:', result.error);
        
        // Enhanced error handling for nHost specific errors
        let errorMessage = result.error.message || 'Failed to change password';
        
        if (result.error.message?.toLowerCase().includes('pwned') || 
            result.error.message?.toLowerCase().includes('compromised') ||
            result.error.message?.toLowerCase().includes('breach')) {
          errorMessage = 'This password has been found in data breaches. Please choose a more unique password.';
        } else if (result.error.message?.toLowerCase().includes('password')) {
          errorMessage = 'Password does not meet security requirements. Use 8+ characters with uppercase, lowercase, number, and symbol.';
        }
        
        toast.error(errorMessage);
        return;
      }

      console.log('✅ Password changed successfully');
      
      // Clear form fields
      newPassword = '';
      confirmPassword = '';
      passwordChangeSuccess = true;
      
      // Show success message with countdown
      toast.success('Password changed successfully! You will be signed out in 5 seconds for security.', {
        duration: 5000
      });
      
      // Start countdown
      countdownInterval = setInterval(() => {
        countdownSeconds--;
        if (countdownSeconds <= 0) {
          if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
          }
        }
      }, 1000);
      
      // Give user time to see the success message before signing out
      signOutTimeout = setTimeout(async () => {
        toast.success('Redirecting to sign in page...', { duration: 2000 });
        await authActions.signOut();
      }, 5000);
      
    } catch (error) {
      console.error('Password change error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
    }
  }

  function cancelAutoSignOut() {
    if (countdownInterval) {
      clearInterval(countdownInterval);
      countdownInterval = null;
    }
    if (signOutTimeout) {
      clearTimeout(signOutTimeout);
      signOutTimeout = null;
    }
    passwordChangeSuccess = false;
    countdownSeconds = 5; // Reset for next time
    toast.success('Auto sign-out cancelled. You can continue using the app.', { duration: 3000 });
  }

  async function handleAccountDeletion(event: Event) {
    event.preventDefault();
    
    // Validate form
    if (deleteForm.confirmText !== 'DELETE') {
      toast.error('Please type DELETE to confirm');
      return;
    }
    
    if (!deleteForm.acknowledgment) {
      toast.error('Please acknowledge that this action is permanent');
      return;
    }

    isDeleting = true;

    try {
      // Get current user session
      const currentSession = $session;
      if (!currentSession?.user?.id) {
        toast.error('No active session found. Please sign in again.');
        throw new Error('No active session');
      }

      console.log('Account deletion requested for user:', currentSession.user.id, 'with reason:', deleteForm.reason);
      
      // Make GraphQL request to mark account for deletion
      const response = await nhost.graphql.request(`
        mutation RequestAccountDeletion($userId: uuid!, $reason: String) {
          update_user_profiles(
            where: {user_id: {_eq: $userId}},
            _set: {
              deletion_requested_at: "now()",
              deletion_reason: $reason
            }
          ) {
            affected_rows
            returning {
              id
              deletion_requested_at
              deletion_reason
            }
          }
        }
      `, {
        userId: currentSession.user.id,
        reason: deleteForm.reason || null
      });

      if (response.error) {
        console.error('❌ Account deletion request failed:', response.error);
        toast.error(response.error.message || 'Failed to submit deletion request');
        throw response.error;
      }

      const result = response.data?.update_user_profiles;
      if (!result?.affected_rows || result.affected_rows === 0) {
        console.error('❌ No user profile found or updated');
        toast.error('Failed to update user profile. Please try again.');
        throw new Error('No rows affected');
      }

      console.log('✅ Account deletion request submitted successfully:', result.returning[0]);
      toast.success('Account deletion request submitted successfully. You will be contacted within 48 hours.');

      // Reset form and close modal
      showDeleteDialog = false;
      deleteForm = { reason: '', confirmText: '', acknowledgment: false };

      // Sign out after successful deletion request
      setTimeout(async () => {
        await authActions.signOut();
      }, 2000);
      
    } catch (error) {
      console.error('Account deletion failed:', error);
      toast.error('Failed to submit deletion request. Please try again.');
    } finally {
      isDeleting = false;
    }
  }
</script>

<svelte:head>
  <title>Profile Settings - SourceFlex</title>
</svelte:head>

<ProtectedRoute>
  <AppLayout>
    <div class="container mx-auto max-w-2xl p-6">
      <h1 class="text-2xl font-bold mb-6">Profile Settings</h1>

  {#if showChangePassword}
    <!-- Password Reset Section -->
    <Card class="mb-6 border-blue-200 bg-blue-50">
      <CardHeader>
        <div class="flex items-center gap-2">
          <CheckCircle class="h-5 w-5 text-blue-600" />
          <h2 class="text-lg font-semibold text-blue-900">Change Your Password</h2>
        </div>
        <p class="text-sm text-blue-700">
          Please set your new password below.
        </p>
      </CardHeader>
      <CardContent>
        <form onsubmit={handlePasswordChange} class="space-y-4">
          <!-- New Password Field -->
          <div class="form-group">
            <Label for="new-password">New Password</Label>
            <div class="password-wrapper relative">
              <Input
                id="new-password"
                type={showPassword ? 'text' : 'password'}
                bind:value={newPassword}
                placeholder="Enter new password"
                disabled={isLoading}
                required
                class="pr-10"
              />
              <button
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                onclick={() => showPassword = !showPassword}
              >
                {#if showPassword}
                  <EyeOff class="h-4 w-4" />
                {:else}
                  <Eye class="h-4 w-4" />
                {/if}
              </button>
            </div>
            <p class="text-xs text-muted-foreground mt-1">
              Must be at least 8 characters long
            </p>
          </div>

          <!-- Confirm Password Field -->
          <div class="form-group">
            <Label for="confirm-password">Confirm New Password</Label>
            <div class="password-wrapper relative">
              <Input
                id="confirm-password"
                type={showConfirmPassword ? 'text' : 'password'}
                bind:value={confirmPassword}
                placeholder="Confirm new password"
                disabled={isLoading}
                required
                class="pr-10"
              />
              <button
                type="button"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
                onclick={() => showConfirmPassword = !showConfirmPassword}
              >
                {#if showConfirmPassword}
                  <EyeOff class="h-4 w-4" />
                {:else}
                  <Eye class="h-4 w-4" />
                {/if}
              </button>
            </div>
          </div>

          <!-- Password Validation Indicators -->
          {#if newPassword && newPassword.length > 0}
            <div class="password-validation">
              <div class="validation-item {newPassword.length >= 8 ? 'valid' : 'invalid'}">
                {newPassword.length >= 8 ? '✓' : '○'} 8+ characters
              </div>
              <div class="validation-item {(/[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword)) ? 'valid' : 'invalid'}">
                {(/[A-Z]/.test(newPassword) && /[a-z]/.test(newPassword)) ? '✓' : '○'} Upper & lowercase
              </div>
              <div class="validation-item {(/\d/.test(newPassword)) ? 'valid' : 'invalid'}">
                {(/\d/.test(newPassword)) ? '✓' : '○'} Number
              </div>
              <div class="validation-item {(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(newPassword)) ? 'valid' : 'invalid'}">
                {(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(newPassword)) ? '✓' : '○'} Special character
              </div>
              {#if confirmPassword && confirmPassword.length > 0}
                <div class="validation-item {newPassword === confirmPassword ? 'valid' : 'invalid'}">
                  {newPassword === confirmPassword ? '✓' : '○'} Passwords match
                </div>
              {/if}
              <div class="validation-item info">
                💡 Use a unique password not found in data breaches
              </div>
            </div>
          {/if}

          <Button type="submit" class="w-full" disabled={isLoading}>
            {#if isLoading}
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Changing password...
            {:else}
              Change Password
            {/if}
          </Button>
        </form>
      </CardContent>
    </Card>
  {/if}

  {#if passwordChangeSuccess}
    <!-- Password Change Success Section -->
    <Card class="mb-6 border-green-200 bg-green-50">
      <CardHeader>
        <div class="flex items-center gap-2">
          <CheckCircle class="h-5 w-5 text-green-600" />
          <h2 class="text-lg font-semibold text-green-900">Password Changed Successfully!</h2>
        </div>
        <p class="text-sm text-green-700">
          Your password has been updated successfully. For security reasons, you will be signed out automatically.
        </p>
      </CardHeader>
      <CardContent>
        <div class="flex items-center justify-center p-4">
          <div class="text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">{countdownSeconds}</div>
            <p class="text-sm text-green-700 mb-3">
              Signing out in {countdownSeconds} second{countdownSeconds !== 1 ? 's' : ''}...
            </p>
            <p class="text-xs text-green-600 mb-4">
              You'll be redirected to the sign-in page to log in with your new password.
            </p>
            <Button 
              variant="outline" 
              size="sm"
              onclick={cancelAutoSignOut}
              class="border-green-600 text-green-700 hover:bg-green-100"
            >
              <X class="h-4 w-4 mr-2" />
              Stay Signed In
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  {/if}

  <!-- Other Settings (placeholder for future) -->
  <Card class="mb-8">
    <CardHeader>
      <h2 class="text-lg font-semibold">Reset Password</h2>
      <p class="text-sm text-muted-foreground">
        Manage your account password.
      </p>
    </CardHeader>
    <CardContent>
      <div class="space-y-4">
        <Button 
          variant="outline" 
          onclick={() => showChangePassword = !showChangePassword}
        >
          {showChangePassword ? 'Hide' : 'Show'} Change Password
        </Button>
        
       
      </div>
    </CardContent>
  </Card>

  <!-- Danger Zone -->
  <Card class="border-red-100">
    <CardHeader class="pb-3">
      <h2 class="text-lg font-semibold text-red-900">Danger Zone</h2>
      <p class="text-sm text-muted-foreground">
        Irreversible actions that will permanently affect your account.
      </p>
    </CardHeader>
    <CardContent>
      <div class="rounded-lg border border-red-200 p-4">
        <div class="flex items-start justify-between">
          <div class="space-y-1">
            <h3 class="font-medium text-red-900">Delete Account</h3>
            <p class="text-sm text-muted-foreground">
              Permanently remove your account and all associated data.
            </p>
          </div>
          <Button 
            variant="outline" 
            size="sm"
            class="border-red-300 text-red-700 hover:bg-red-50"
            onclick={() => showDeleteDialog = true}
          >
            <Trash2 class="h-4 w-4 mr-2" />
            Delete Account
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</div>
  </AppLayout>
</ProtectedRoute>

<!-- Inline Account Deletion Modal (No Portal) -->
{#if showDeleteDialog}
  <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
    <div class="relative bg-white rounded-lg shadow-lg max-w-md w-full mx-4 p-6">
                     
      <div class="flex gap-2 mb-4">
        <AlertTriangle class="h-5 text-red-600" />
        <h2 class="text-lg font-semibold text-red-900">Delete Account</h2>
        <button 
          onclick={() => showDeleteDialog = false}
          class="absolute right-4 top-4 rounded-sm opacity-70 hover:opacity-100"
        >
          <X class="h-4 w-4" />
        </button>
      </div>
      
      <p class="text-sm text-gray-600 mb-6">
        This action is permanent and cannot be undone. All your data will be removed.
      </p>

      <!-- Form -->
      <form onsubmit={handleAccountDeletion} class="space-y-4">
        <!-- Reason -->
        <div class="space-y-2">
          <Label class="text-sm font-medium">Reason (optional)</Label>
          <Textarea
            bind:value={deleteForm.reason}
            placeholder="Help us improve by sharing why you're leaving..."
            class="min-h-[80px] resize-none"
          />
        </div>

        <!-- Confirmation -->
        <div class="space-y-2">
          <Label class="text-sm font-medium">
            Type <span class="font-mono font-bold text-red-600">DELETE</span> to confirm
          </Label>
          <Input
            bind:value={deleteForm.confirmText}
            placeholder="DELETE"
            class="font-mono"
          />
        </div>

        <!-- Acknowledgment -->
        <div class="flex items-start space-x-2">
          <input
            type="checkbox"
            id="acknowledgment"
            bind:checked={deleteForm.acknowledgment}
            class="mt-1"
          />
          <Label for="acknowledgment" class="text-sm text-gray-600 leading-tight">
            I understand this action is permanent and will delete all my account data
          </Label>
        </div>

        <!-- Actions -->
        <div class="flex justify-end gap-2 pt-4">
          <Button 
            type="button"
            variant="outline" 
            size="sm"
            onclick={() => showDeleteDialog = false}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="destructive"
            size="sm"
            disabled={isDeleting || deleteForm.confirmText !== 'DELETE' || !deleteForm.acknowledgment}
            class="min-w-[100px]"
          >
            {#if isDeleting}
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Deleting...
            {:else}
              <Trash2 class="h-4 w-4 mr-2" />
              Delete Account
            {/if}
          </Button>
        </div>
      </form>
    </div>
  </div>
{/if}

<style>
  .password-wrapper {
    position: relative;
  }

  .password-validation {
    display: flex;
    gap: 0.75rem;
    font-size: 0.75rem;
    margin-top: 0.75rem;
    flex-wrap: wrap;
    animation: validationSlideIn 0.4s ease-out;
  }

  @keyframes validationSlideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
    to {
      opacity: 1;
      transform: translateY(0);
      max-height: 50px;
    }
  }

  .validation-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .validation-item.valid {
    color: rgb(22, 163, 74);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.2);
    transform: scale(1.05);
    animation: validationSuccess 0.3s ease-out;
  }

  .validation-item.invalid {
    color: hsl(var(--muted-foreground));
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .validation-item.info {
    color: rgb(59, 130, 246);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    border: 1px solid rgba(59, 130, 246, 0.2);
    font-size: 0.7rem;
  }

  @keyframes validationSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1.05); }
  }
</style>