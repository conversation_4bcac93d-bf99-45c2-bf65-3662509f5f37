<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import { session } from '$lib/stores/auth.js';
  import { onMount } from 'svelte';
  import type { OrgUser } from '$lib/types/auth.js';

  let orgUsers = $state<OrgUser[]>([]);
  let isLoading = $state(true);

  onMount(async () => {
    await loadOrgUsers();
    isLoading = false;
  });

  async function loadOrgUsers() {
    try {
      // TODO: Implement GraphQL query to fetch organization users
      // This would use the GetOrgUsers query from org-users.gql
      console.log('Loading organization users for org_manager:', $session?.user?.id);
      
      // Mock data for now - replace with actual GraphQL call
      const mockUsers: OrgUser[] = [
        {
          id: '1',
          user_id: 'user-1',
          current_desk: 'recruitment',
          email_domain: 'company.com',
          business_name: 'Example Corp',
          is_active: true,
          created_at: '2024-01-15T10:00:00Z',
          updated_at: '2024-01-15T10:00:00Z',
          timezone: 'UTC',
          onboarding_completed: true,
          user: {
            email: '<EMAIL>',
            displayName: '<PERSON> Doe'
          }
        },
        {
          id: '2',
          user_id: 'user-2',
          current_desk: 'bench_sales',
          email_domain: 'company.com',
          business_name: 'Example Corp',
          is_active: true,
          created_at: '2024-01-20T14:30:00Z',
          updated_at: '2024-01-20T14:30:00Z',
          timezone: 'UTC',
          onboarding_completed: true,
          user: {
            email: '<EMAIL>',
            displayName: 'Jane Smith'
          }
        }
      ];
      
      orgUsers = mockUsers;
    } catch (error) {
      console.error('Error loading organization users:', error);
    }
  }

  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString();
  }

  function getDeskBadgeClass(desk: string): string {
    return desk === 'recruitment' 
      ? 'bg-blue-100 text-blue-800' 
      : 'bg-green-100 text-green-800';
  }
</script>

<svelte:head>
  <title>Organization Users - SourceFlex</title>
</svelte:head>

<ProtectedRoute allowedRoles={['org_manager', 'sf_admin']}>
  {#snippet children()}
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation Header -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <a href="/dashboard" class="text-blue-600 hover:text-blue-500 mr-4" aria-label="Back to dashboard">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </a>
              <h1 class="text-xl font-semibold text-gray-900">Organization Users</h1>
            </div>
            
            <div class="flex items-center">
              <span class="text-sm text-gray-500">
                Role: {$session?.user?.defaultRole}
              </span>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
          
          {#if isLoading}
            <div class="flex items-center justify-center py-12">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          {:else}
            <!-- Users Table -->
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
              <div class="px-4 py-5 sm:p-6">
                <div class="sm:flex sm:items-center sm:justify-between mb-6">
                  <div>
                    <h2 class="text-lg font-medium text-gray-900">Team Members</h2>
                    <p class="mt-1 text-sm text-gray-500">
                      Manage users in your organization
                    </p>
                  </div>
                  <div class="mt-4 sm:mt-0">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {orgUsers.length} user{orgUsers.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>

                {#if orgUsers.length === 0}
                  <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM9 9a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">No team members</h3>
                    <p class="mt-1 text-sm text-gray-500">
                      No users found in your organization.
                    </p>
                  </div>
                {:else}
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-50">
                        <tr>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User
                          </th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Current Desk
                          </th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Company
                          </th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                          <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Joined
                          </th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        {#each orgUsers as user}
                          <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                              <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                  <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700">
                                      {user.user.displayName ? user.user.displayName.charAt(0).toUpperCase() : user.user.email.charAt(0).toUpperCase()}
                                    </span>
                                  </div>
                                </div>
                                <div class="ml-4">
                                  <div class="text-sm font-medium text-gray-900">
                                    {user.user.displayName || 'No name'}
                                  </div>
                                  <div class="text-sm text-gray-500">
                                    {user.user.email}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {getDeskBadgeClass(user.current_desk)}">
                                {user.current_desk === 'recruitment' ? 'Recruitment' : 'Bench Sales'}
                              </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {user.business_name || 'Not specified'}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                              {#if user.is_active}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                  Active
                                </span>
                              {:else}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                  Inactive
                                </span>
                              {/if}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                              {formatDate(user.created_at)}
                            </td>
                          </tr>
                        {/each}
                      </tbody>
                    </table>
                  </div>
                {/if}
              </div>
            </div>
          {/if}
        </div>
      </main>
    </div>
  {/snippet}
</ProtectedRoute>
