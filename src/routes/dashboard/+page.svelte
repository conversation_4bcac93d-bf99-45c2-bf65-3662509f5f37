<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';
  import DeskSwitcher from '$lib/components/DeskSwitcher.svelte';
  import { Button } from '$lib/components/ui/button';
  import { session, userProfile, authActions } from '$lib/stores/auth.js';
  import { onMount } from 'svelte';
  import toast from 'svelte-5-french-toast';
  let isLoading = $state(true);

  onMount(async () => {
    console.log('Dashboard - onMount called, session:', $session?.user?.id);
    // Load user profile when component mounts
    try {
      if ($session?.user) {
        await loadUserProfile();
      }
    } catch (error) {
      console.error('Dashboard - Error in onMount:', error);
    } finally {
      console.log('Dashboard - Setting isLoading to false');
      isLoading = false;
    }
  });

  // Also set up a reactive effect to handle session changes
  $effect(() => {
    // If session changes after initial mount, update loading state
    if ($session?.user && isLoading) {
      console.log('Dashboard - Session detected via effect, stopping loading');
      isLoading = false;
    }
  });

  async function loadUserProfile() {
    // This would typically use a GraphQL query
    // For now, we'll simulate profile loading
    try {
      // TODO: Implement actual GraphQL query to fetch user profile
      console.log('Loading user profile for:', $session?.user?.id);
      
      // Simulated profile data - replace with actual GraphQL call
      const mockProfile = {
        id: '1',
        user_id: $session?.user?.id || '',
        current_desk: 'recruitment' as const,
        email_domain: extractEmailDomain($session?.user?.email || ''),
        business_name: undefined,
        timezone: 'UTC',
        onboarding_completed: false,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      userProfile.set(mockProfile);
    } catch (error) {
      console.error('Error loading user profile:', error);
    }
  }

  function extractEmailDomain(email: string): string {
    const domain = email.split('@')[1];
    if (!domain) return '';
    const parts = domain.split('.');
    if (parts.length > 2) {
      return parts.slice(-2).join('.');
    }
    return domain;
  }

  async function handleDeskChange(newDesk: 'recruitment' | 'bench_sales') {
    try {
      // TODO: Implement GraphQL mutation to update user profile
      console.log('Switching desk to:', newDesk);
      
      // Update local store immediately
      userProfile.update(profile => profile ? {...profile, current_desk: newDesk} : null);
      
      toast.success(`Switched to ${newDesk === 'recruitment' ? 'Recruitment' : 'Bench Sales'} desk`);
    } catch (error) {
      console.error('Error updating desk:', error);
      toast.error('Failed to switch desk');
    }
  }
</script>

<svelte:head>
  <title>Dashboard - SourceFlex</title>
</svelte:head>

<ProtectedRoute>
  {#snippet children()}
    <AppLayout>
      {#snippet children()}
        <div class="p-6">
          {#if isLoading}
            <div class="flex items-center justify-center py-12">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          {:else}
            <!-- Page Header -->
            <div class="mb-6">
              <h1 class="text-2xl font-bold text-foreground">Dashboard</h1>
              <p class="text-muted-foreground">Welcome back to SourceFlex</p>
            </div>

            <!-- Welcome Section -->
            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-6">
              <!-- Profile Info Card -->
              <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                <h3 class="font-semibold mb-4">Profile Information</h3>
                <div class="space-y-2">
                  <div>
                    <p class="text-sm text-muted-foreground">Email</p>
                    <p class="text-sm font-medium">{$session?.user?.email}</p>
                  </div>
                  <div>
                    <p class="text-sm text-muted-foreground">Email Domain</p>
                    <p class="text-sm font-medium">{$userProfile?.email_domain || 'N/A'}</p>
                  </div>
                  <div>
                    <p class="text-sm text-muted-foreground">Status</p>
                    {#if $userProfile?.onboarding_completed}
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        Active
                      </span>
                    {:else}
                      <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        Onboarding Pending
                      </span>
                    {/if}
                  </div>
                </div>
              </div>

              <!-- Desk Management Card -->
              <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                <h3 class="font-semibold mb-4">Desk Management</h3>
                <DeskSwitcher onDeskChange={handleDeskChange} />
                {#if !$userProfile?.onboarding_completed}
                  <div class="mt-4">
                    <a 
                      href="/profile" 
                      class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-primary-foreground bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                    >
                      Complete Setup
                    </a>
                  </div>
                {/if}
              </div>

              <!-- Quick Stats Card -->
              <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
                <h3 class="font-semibold mb-4">Quick Stats</h3>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Active Jobs</span>
                    <span class="text-sm font-medium">-</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Candidates</span>
                    <span class="text-sm font-medium">-</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-sm text-muted-foreground">Clients</span>
                    <span class="text-sm font-medium">-</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Quick Actions -->
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <h3 class="font-semibold mb-4">Quick Actions</h3>
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <a 
                  href="/jobs" 
                  class="flex items-center p-4 border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
                >
                  <div class="flex-shrink-0 mr-3">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium">View Jobs</p>
                    <p class="text-sm text-muted-foreground">Manage job listings</p>
                  </div>
                </a>
                
                <a 
                  href="/candidates" 
                  class="flex items-center p-4 border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
                >
                  <div class="flex-shrink-0 mr-3">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium">Candidates</p>
                    <p class="text-sm text-muted-foreground">Browse candidate database</p>
                  </div>
                </a>
                
                <a 
                  href="/clients" 
                  class="flex items-center p-4 border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
                >
                  <div class="flex-shrink-0 mr-3">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium">Clients</p>
                    <p class="text-sm text-muted-foreground">Manage client relationships</p>
                  </div>
                </a>
                
                <a 
                  href="/bench" 
                  class="flex items-center p-4 border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors"
                >
                  <div class="flex-shrink-0 mr-3">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 22 12 18.27 5.82 22 7 13.87 2 9l6.91-.74L12 2z" />
                    </svg>
                  </div>
                  <div>
                    <p class="font-medium">Bench Sales</p>
                    <p class="text-sm text-muted-foreground">Available consultants</p>
                  </div>
                </a>
              </div>
            </div>
          {/if}
        </div>
      {/snippet}
    </AppLayout>
  {/snippet}
</ProtectedRoute>
