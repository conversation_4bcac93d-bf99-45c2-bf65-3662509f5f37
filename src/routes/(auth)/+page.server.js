import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // Development-only logging for auth debugging
    if (process.env.NODE_ENV === 'development') {
        console.log('🔍 Auth route params:', {
            all: Object.fromEntries(url.searchParams.entries()),
            type: url.searchParams.get('type'),
            ticket: url.searchParams.get('ticket'),
            refreshToken: url.searchParams.get('refreshToken'),
            token: url.searchParams.get('token'),
            redirectTo: url.searchParams.get('redirectTo'),
            email: url.searchParams.get('email')
        });
    }
    
    // Let nHost handle redirects naturally via redirectTo parameter
    // We no longer intercept email verification or password reset redirects
    // nHost will automatically redirect users to the configured redirectTo URLs:
    // - Email verification → /welcome
    // - Password reset → /profile/settings?action=change-password
    
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    const envConfig = getEnvironmentConfig();
    
    // Return data for unauthenticated users (login/register page)
    return {
        authenticated: false,
        turnstileSiteKey: envConfig.turnstile.siteKey
    };
};
