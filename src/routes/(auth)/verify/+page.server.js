import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // This page now serves as a fallback for edge cases
    // The primary email verification flow uses redirectTo → /welcome
    
    console.log('📧 Verify page fallback accessed:', {
        userAuthenticated: !!locals.user,
        userEmail: locals.user?.email,
        hasParams: url.searchParams.toString()
    });
    
    // If user is authenticated, they likely already verified - redirect to welcome
    if (locals.user) {
        console.log('✅ User already authenticated - redirecting to welcome');
        throw redirect(307, '/welcome');
    }
    
    // Check for verification parameters from nHost (legacy support)
    const ticket = url.searchParams.get('ticket');        
    const refreshToken = url.searchParams.get('refreshToken'); 
    const type = url.searchParams.get('type');
    const email = url.searchParams.get('email');
    
    // If we have verification parameters, process them (legacy support)
    const verificationToken = ticket || refreshToken;
    
    if (!verificationToken) {
        console.log('❌ No verification token - redirecting to resend');
        throw redirect(307, '/resendverification');
    }
    
    const envConfig = getEnvironmentConfig();
    
    // Return data for legacy email verification support
    return {
        authenticated: false,
        ticket: ticket,
        refreshToken: refreshToken,
        verificationToken: verificationToken,
        type: type,
        email: email,
        isTicket: !!ticket,
        turnstileSiteKey: envConfig.turnstile.siteKey
    };
};
