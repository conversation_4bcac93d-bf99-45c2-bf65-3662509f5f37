import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // This page now serves as a fallback for edge cases where users land here directly
    // The primary password reset flow uses redirectTo → /profile/settings?action=change-password
    
    console.log('🔑 Reset password fallback page accessed:', {
        userAuthenticated: !!locals.user,
        userEmail: locals.user?.email,
        hasParams: url.searchParams.toString()
    });
    
    // If user is authenticated, redirect them to the proper settings page
    if (locals.user) {
        console.log('✅ Authenticated user - redirecting to settings');
        throw redirect(307, '/profile/settings?action=change-password');
    }
    
    // For unauthenticated users who somehow land here, redirect to forgot password
    console.log('❌ Unauthenticated user on reset page - redirecting to forgot password');
    throw redirect(307, '/forgotpassword?message=please_request_reset');
};
