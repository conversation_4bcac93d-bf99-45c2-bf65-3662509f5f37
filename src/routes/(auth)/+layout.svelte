<!-- Enterprise Auth Layout - Clean, branded design for authentication pages -->
<div class="auth-layout bg-background text-foreground">
  <!-- Header with Logo -->
  <header class="auth-header">
    <div class="logo-container">
      <img src="/logo.svg" alt="SourceFlex" class="logo" />
    </div>
  </header>

  <!-- Main Content Area -->
  <main class="auth-main">
    <slot />
  </main>

  <!-- Footer (optional branding space) -->
  <footer class="auth-footer">
    <p class="footer-text">© 2025 SourceFlex. For Support and Suggestions, please <NAME_EMAIL> </p>
  </footer>
</div>

<style>
  .auth-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
  }

  .auth-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    padding: 1.5rem 2rem;
    backdrop-filter: blur(12px);
    border-bottom: 1px solid hsl(var(--border));
    animation: headerSlideDown 0.8s ease-out;
  }

  @keyframes headerSlideDown {
    from {
      opacity: 0;
      transform: translateY(-100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .logo-container {
    transition: transform 0.3s ease;
  }

  .logo-container:hover {
    transform: scale(1.05);
  }

  .logo {
    height: 40px;
    width: auto;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  }

  .logo:hover {
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15)) brightness(1.05);
    transform: translateY(-1px);
  }

  .auth-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 7rem 1rem 4rem;
    min-height: calc(100vh - 120px);
    position: relative;
    z-index: 10;
    animation: mainFadeIn 1s ease-out 0.3s both;
  }

  @keyframes mainFadeIn {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .auth-footer {
    padding: 1.5rem 2rem;
    backdrop-filter: blur(12px);
    border-top: 1px solid hsl(var(--border));
    position: relative;
    z-index: 10;
    animation: footerSlideUp 0.8s ease-out 0.5s both;
  }

  @keyframes footerSlideUp {
    from {
      opacity: 0;
      transform: translateY(100%);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .footer-text {
    text-align: center;
    font-size: 0.8rem;
    color: hsl(var(--muted-foreground));
    margin: 0;
    max-width: 1200px;
    margin: 0 auto;
    font-weight: 500;
    letter-spacing: 0.025em;
    animation: textGlow 3s ease-in-out infinite alternate;
  }

  @keyframes textGlow {
    from {
      opacity: 0.7;
    }
    to {
      opacity: 1;
    }
  }

  /* Mobile Responsiveness */
  @media (max-width: 640px) {
    .auth-header {
      padding: 1.25rem 1rem;
    }
    
    .logo {
      height: 36px;
    }
    
    .auth-main {
      padding: 6rem 1rem 2rem;
    }
    
    .auth-footer {
      padding: 1rem;
    }

    .footer-text {
      font-size: 0.75rem;
    }
  }

  /* Enterprise-grade focus styles */
  .logo:focus-visible {
    outline: 2px solid #2563eb;
    outline-offset: 2px;
    border-radius: 4px;
  }


</style>