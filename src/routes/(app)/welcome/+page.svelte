<script lang="ts">
	import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
	import AppLayout from '$lib/components/layouts/AppLayout.svelte';
	import OnboardingFlow from '$lib/components/onboarding/OnboardingFlow.svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Button } from '$lib/components/ui/button';
	import { goto } from '$app/navigation';
	import { session } from '$lib/stores/auth.js';
	import { UpdateUserProfileOnboardingStore } from '$houdini';
	import toast from 'svelte-5-french-toast';
	import { page } from '$app/stores';

	// Use the generated Houdini store
	const updateUserProfile = new UpdateUserProfileOnboardingStore();

	// Component state
	let isLoading = $state(false);
	let showOnboarding = $state(true);

	// Form data
	let onboardingData = $state({
		firstName: '',
		lastName: '',
		jobTitle: '',
		businessName: '',
		phoneNumber: '',
		currentDesk: 'recruitment' as 'recruitment' | 'bench_sales',
		timezone: ''
	});

	async function handleOnboardingSubmit(data: typeof onboardingData) {
		if (!$session?.user?.id) {
			toast.error('Authentication error. Please refresh the page.');
			return;
		}

		isLoading = true;
		
		try {
			const result = await updateUserProfile.mutate({
				userId: $session.user.id,
				firstName: data.firstName,
				lastName: data.lastName,
				jobTitle: data.jobTitle,
				businessName: data.businessName,
				phoneNumber: data.phoneNumber || null,
				currentDesk: data.currentDesk,
				timezone: data.timezone
			});

			if (result.errors) {
				throw new Error(result.errors[0]?.message || 'Failed to update profile');
			}

			if (result.data?.update_user_profiles?.affected_rows === 0) {
				throw new Error('No profile was updated');
			}

			// Success!
			toast.success('🎉 Congratulations! Your profile has been completed');
			
			// Wait a moment for the toast to show, then redirect
			setTimeout(() => {
				goto('/dashboard');
			}, 1500);

		} catch (error) {
			console.error('Onboarding error:', error);
			toast.error(error instanceof Error ? error.message : 'Failed to complete onboarding');
		} finally {
			isLoading = false;
		}
	}

	function skipOnboarding() {
		goto('/dashboard');
	}
</script>

<ProtectedRoute>
	<AppLayout>
		<div class="container mx-auto py-6 sm:py-12 px-4">
			{#if showOnboarding}
				<!-- Welcome Header -->
				<div class="text-center mb-6 sm:mb-8">
					<h1 class="text-2xl sm:text-3xl font-bold mb-2">Welcome to SourceFlex!</h1>
					<p class="text-sm sm:text-base text-muted-foreground mb-4">
						Your email has been verified successfully. Let's get you set up.
					</p>
					<Button 
						variant="ghost" 
						size="sm" 
						onclick={skipOnboarding}
						class="text-xs sm:text-sm"
					>
						Skip setup for now
					</Button>
				</div>

				<!-- Onboarding Flow -->
				<OnboardingFlow
					bind:formData={onboardingData}
					{isLoading}
					on:submit={e => handleOnboardingSubmit(e.detail)}
				/>
			{:else}
				<!-- Fallback Welcome Card -->
				<div class="max-w-2xl mx-auto">
					<Card>
						<CardHeader class="text-center">
							<CardTitle class="text-2xl font-bold">Welcome to SourceFlex!</CardTitle>
							<p class="text-muted-foreground">
								Your email has been verified successfully. Let's get you started.
							</p>
						</CardHeader>
						<CardContent class="space-y-6">
							<div class="text-center">
								<h3 class="text-lg font-semibold mb-2">
									Hello, {$session?.user?.displayName || $session?.user?.email}!
								</h3>
								<p class="text-muted-foreground">
									We're excited to have you on board. Your journey with SourceFlex starts here.
								</p>
							</div>
							
							<div class="flex justify-center">
								<Button onclick={() => goto('/dashboard')} class="w-full max-w-sm">
									Continue to Dashboard
								</Button>
							</div>
						</CardContent>
					</Card>
				</div>
			{/if}
		</div>
	</AppLayout>
</ProtectedRoute>
